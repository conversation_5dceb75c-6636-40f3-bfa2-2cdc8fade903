{"name": "easefood-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@paystack/inline-js": "^2.22.3", "@rails/actioncable": "^8.0.100", "@reduxjs/toolkit": "^2.2.7", "@vitejs/plugin-react": "^4.3.1", "axios": "^1.7.4", "chart.js": "^4.4.9", "lucide-react": "^0.471.0", "prop-types": "^15.8.1", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-icons": "^5.3.0", "react-redux": "^9.1.2", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^6.26.1", "react-toastify": "^11.0.5", "vite": "^5.4.1", "vite-plugin-pwa": "^1.0.0", "workbox-cacheable-response": "^7.3.0", "workbox-expiration": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/eslint-parser": "^7.25.1", "@babel/plugin-syntax-jsx": "^7.24.7", "@babel/preset-react": "^7.24.7", "@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-legacy": "^5.4.3", "eslint": "^7.32.0", "eslint-config-airbnb": "^18.2.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "stylelint": "^13.13.1", "stylelint-config-standard": "^21.0.0", "stylelint-csstree-validator": "^1.9.0", "stylelint-scss": "^3.21.0"}}