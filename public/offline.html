<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>EaseFood - Offline</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f9f9f9;
      color: #333;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      text-align: center;
    }
    .container {
      max-width: 500px;
      padding: 2rem;
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    .logo {
      width: 100px;
      height: 100px;
      margin-bottom: 1.5rem;
      background-color: #ff9900;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1.5rem;
    }
    .logo svg {
      width: 60px;
      height: 60px;
      color: white;
    }
    h1 {
      font-size: 1.8rem;
      margin: 0 0 1rem;
      color: #ff9900;
    }
    p {
      margin: 0 0 1.5rem;
      line-height: 1.5;
      color: #666;
    }
    .button {
      display: inline-block;
      padding: 0.75rem 1.5rem;
      background-color: #ff9900;
      color: white;
      text-decoration: none;
      border-radius: 6px;
      font-weight: 600;
      transition: background-color 0.2s;
    }
    .button:hover {
      background-color: #e68a00;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="logo">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M17 8.5H3a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-9a2 2 0 0 0-2-2Z"></path>
        <path d="M8 8.5V7a4 4 0 0 1 8 0v1.5"></path>
        <path d="M12 13v3"></path>
      </svg>
    </div>
    <h1>You're Offline</h1>
    <p>It looks like you're currently offline. Please check your internet connection and try again.</p>
    <p>Don't worry, you can still access previously visited pages while offline.</p>
    <a href="/" class="button">Try Again</a>
  </div>
</body>
</html>
