name: Deploy EaseFood Frontend to Namecheap VPS
on:
  push:
    branches: [ development ]
  pull_request:
    branches: [ development ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      # - name: Run tests
      #   run: npm run test -- --run
        
      - name: Build application
        run: npm run build
        
      - name: Debug build output
        run: |
          echo "=== Build output structure ==="
          ls -la dist/
          echo ""
          echo "=== Looking for index.html ==="
          find dist/ -name "index.html" -ls || echo "No index.html found"
          echo ""
          echo "=== All files in dist/ ==="
          find dist/ -type f | head -20
          echo ""
          echo "=== Archive contents preview ==="
          tar -tzf <(tar -czf - -C dist .) | head -10
        
      - name: Create deployment archive
        run: |
          tar -czf easefood-frontend-build.tar.gz -C dist .
          
      - name: Deploy to VPS
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          key: ${{ secrets.VPS_SSH_KEY }}
          port: ${{ secrets.VPS_PORT }}
          source: "easefood-frontend-build.tar.gz"
          target: "/tmp/"
          
      - name: Extract and setup on VPS
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          key: ${{ secrets.VPS_SSH_KEY }}
          port: ${{ secrets.VPS_PORT }}
          script: |
            DEPLOY_PATH="/var/www/html/EaseFood-Frontend"
            
            echo "=== Starting deployment ==="
            
            # Backup current deployment if it exists (to /home/<USER>/backups)
            if [ -d "$DEPLOY_PATH" ]; then
              mkdir -p /home/<USER>/backups
              sudo cp -r "$DEPLOY_PATH" \
                "/home/<USER>/backups/EaseFood-Frontend_backup_$(date +%Y%m%d_%H%M%S)"
              echo "✓ Backup created in /home/<USER>/backups"
            fi
            
            # Remove old deployment and create fresh directory
            sudo rm -rf "$DEPLOY_PATH"
            sudo mkdir -p "$DEPLOY_PATH"
            echo "✓ Fresh deployment directory created"
            
            # Extract build into deployment directory
            cd /tmp
            echo "=== Extracting archive ==="
            sudo tar -xzf easefood-frontend-build.tar.gz -C "$DEPLOY_PATH"
            
            # Debug: Check what was extracted
            echo "=== Files extracted to deployment directory ==="
            ls -la "$DEPLOY_PATH"
            echo ""
            
            # Check for index.html specifically
            if [ -f "$DEPLOY_PATH/index.html" ]; then
              echo "✓ index.html found"
              echo "index.html size: $(wc -c < "$DEPLOY_PATH/index.html") bytes"
            else
              echo "✗ index.html NOT found"
              echo "=== Looking for HTML files ==="
              find "$DEPLOY_PATH" -name "*.html" -ls || echo "No HTML files found"
              echo ""
              echo "=== All files in deployment directory ==="
              find "$DEPLOY_PATH" -type f | head -20
            fi
            
            # Fix permissions so Nginx (www-data) can read everything.
            sudo chown -R www-data:www-data "$DEPLOY_PATH"
            sudo find "$DEPLOY_PATH" -type f -exec chmod 644 {} \;
            sudo find "$DEPLOY_PATH" -type d -exec chmod 755 {} \;

            echo "✓ Permissions set (files=644, dirs=755)"
            
            # Debug: Check final permissions.
            echo "=== Final permissions check ==="
            ls -la "$DEPLOY_PATH"
            
            # Test Nginx configuration.
            echo "=== Testing Nginx configuration ==="
            sudo nginx -t
            
            # Reload Nginx.
            sudo systemctl reload nginx
            echo "✓ Nginx reloaded"
            
            # Test local access
            echo "=== Local curl test ==="
            curl -I http://localhost 2>/dev/null | head -5 || echo "Local HTTP curl failed"
            curl -I https://localhost 2>/dev/null | head -5 || echo "Local HTTPS curl failed"
            
            # Check Nginx error logs for any recent errors
            echo "=== Recent Nginx errors ==="
            tail -10 /var/log/nginx/error.log || echo "No recent errors"
            
            # Clean up
            sudo rm -f /tmp/easefood-frontend-build.tar.gz
            echo "EaseFood Frontend deployment completed!"
            
      # health check to see deployment results
      - name: Health check
        run: |
          sleep 30
          curl -f https://easefood.org || exit 1