// Towns outside Dunkwa that require special delivery zone selection
export const OUTSIDE_DUNKWA_TOWNS = [
  '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>', 
  '<PERSON>bianiha',
  'Agya Adu Estate',
  'Rakia Junction',
  'Motel',
  '<PERSON>gye M<PERSON>',
  'Mmradan',
  'Mmradan New Site'
];

// Helper function to check if an address contains any outside Dunkwa towns
export const containsOutsideTown = (address) => {
  if (!address || typeof address !== 'string') return false;
  
  const normalizedAddress = address.toLowerCase().trim();
  
  return OUTSIDE_DUNKWA_TOWNS.some(town => 
    normalizedAddress.includes(town.toLowerCase())
  );
};

// Helper function to get the town that was found in the address
export const getDetectedTown = (address) => {
  if (!address || typeof address !== 'string') return null;
  
  const normalizedAddress = address.toLowerCase().trim();
  
  return OUTSIDE_DUNKWA_TOWNS.find(town => 
    normalizedAddress.includes(town.toLowerCase())
  );
};
