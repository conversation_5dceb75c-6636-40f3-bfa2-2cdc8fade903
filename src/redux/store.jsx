import { configureStore } from '@reduxjs/toolkit';
import authReducer from './slice/authSlice';
import vendorsReducer from './slice/vendersSlice';
import cartsReducer from './slice/cartsSlice';
import ordersReducer from './slice/ordersSlice';
import ridersReducer from './slice/ridersSlice';
import foodsReducer from './slice/foodsSlice';
import usersReducer from './slice/userSlice';
import adminReducer from './slice/adminSlice';

const store = configureStore({
  reducer: {
    auth: authReducer,
    vendors: vendorsReducer,
    cart: cartsReducer,
    orders: ordersReducer,
    riders: ridersReducer,
    foods: foodsReducer,
    user: usersReducer,
    admin: adminReducer,
  },
});
export default store;
