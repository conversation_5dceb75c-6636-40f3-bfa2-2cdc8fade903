import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import ApiUrl from '../../components/helper-functions/ApiUrl';

export const fetchCartItems = createAsyncThunk('cart/fetchCartItems', async (customerId) => {
  const response = await axios.get(`${ApiUrl}/customers/${customerId}/carts`);
  return Array.isArray(response.data) ? response.data : [];
});

export const addCartItem = createAsyncThunk(
  'cart/addCartItem',
  async ({ customerId, foodId, quantity, priceIndex }) => {
    const cartData = {
      customer_id: customerId,
      food_id: foodId,
      quantity,
    };

    // Add price index if provided (for multiple price support)
    if (priceIndex !== undefined) {
      cartData.selected_price_index = priceIndex;
    }

    const response = await axios.post(`${ApiUrl}/customers/${customerId}/carts`, {
      cart: cartData,
    });

    return response.data;
  },
);

export const removeCartItem = createAsyncThunk(
  'cart/removeCartItem',
  async ({ customerId, cartItemId }) => {
    await axios.delete(`${ApiUrl}/customers/${customerId}/carts/${cartItemId}`);
    return cartItemId;
  },
);

export const updateCartItem = createAsyncThunk(
  'cart/updateCartItem',
  async ({ customerId, cartId, quantity, priceIndex }) => {
    const updateData = {
      quantity,
    };

    // Add price index if provided
    if (priceIndex !== undefined) {
      updateData.selected_price_index = priceIndex;
    }

    const response = await axios.patch(`${ApiUrl}/customers/${customerId}/carts/${cartId}`, {
      cart: updateData,
    });
    return response.data;
  },
);

export const clearCart = createAsyncThunk(
  'cart/clearCart',
  async (customerId) => {
    await axios.delete(`${ApiUrl}/customers/${customerId}/carts/clear`);
    return [];
  },
);

const cartsSlice = createSlice({
  name: 'cart',
  initialState: {
    items: [],
    status: 'idle',
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchCartItems.pending, (state) => ({
        ...state,
        status: 'loading',
      }))
      .addCase(fetchCartItems.fulfilled, (state, action) => {
        const items = action.payload;

        // Ensure all items have proper food structure and selected_price_index from backend
        const normalizedItems = items.map(item => ({
          ...item,
          food: item.food ? {
            ...item.food,
            prices: item.food.prices || [item.food.price],
            has_multiple_prices: item.food.has_multiple_prices || false,
            min_price: item.food.min_price || item.food.price,
            max_price: item.food.max_price || item.food.price
          } : null,
          selected_price_index: item.selected_price_index ?? 0,
        }));

        return {
          ...state,
          status: 'succeeded',
          items: normalizedItems,
        };
      })
      .addCase(fetchCartItems.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))
      .addCase(addCartItem.fulfilled, (state, action) => {
        const newItem = {
          ...action.payload,
          // Ensure food data has price structure
          food: action.payload.food ? {
            ...action.payload.food,
            prices: action.payload.food.prices || [action.payload.food.price],
            has_multiple_prices: action.payload.food.has_multiple_prices || false,
            min_price: action.payload.food.min_price || action.payload.food.price,
            max_price: action.payload.food.max_price || action.payload.food.price
          } : null,
          // Ensure selected_price_index exists
          selected_price_index: action.payload.selected_price_index || 0
        };

        state.items.push(newItem);
      })
      .addCase(addCartItem.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))
      .addCase(removeCartItem.fulfilled, (state, action) => ({
        ...state,
        items: state.items.filter((item) => item.id !== action.payload),
      }))
      .addCase(removeCartItem.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))
      .addCase(updateCartItem.fulfilled, (state, action) => {
        const index = state.items.findIndex((item) => item.id === action.payload.id);
        if (index !== -1) {
          // Retain the existing food data and ensure price structure
          const existingFood = state.items[index].food;
          const updatedItem = {
            ...state.items[index],
            ...action.payload,
            food: action.payload.food ? {
              ...action.payload.food,
              prices: action.payload.food.prices || [action.payload.food.price],
              has_multiple_prices: action.payload.food.has_multiple_prices || false,
              min_price: action.payload.food.min_price || action.payload.food.price,
              max_price: action.payload.food.max_price || action.payload.food.price
            } : existingFood,
            // Ensure selected_price_index is updated
            selected_price_index: action.payload.selected_price_index !== undefined
              ? action.payload.selected_price_index
              : state.items[index].selected_price_index || 0
          };
          const newItems = [...state.items];
          newItems[index] = updatedItem;
          return {
            ...state,
            items: newItems,
          };
        }
        return state;
      })
      .addCase(updateCartItem.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))
      .addCase(clearCart.fulfilled, (state) => ({
        ...state,
        items: [],
      }))
      .addCase(clearCart.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }));
  },
});

export default cartsSlice.reducer;

export const selectCarts = (state) => state.cart.items;
export const selectCartsStatus = (state) => state.cart.status;
export const selectCartsError = (state) => state.cart.error;
