import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import ApiUrl from '../../components/helper-functions/ApiUrl';

const roleEndpoints = {
  customer: 'customers',
  vendor: 'vendors',
  rider: 'riders',
  admin: 'admins',
};

export const fetchUserData = createAsyncThunk(
  'user/fetchUserData',
  async ({ role, userId }, { rejectWithValue }) => {
    try {
      const endpoint = roleEndpoints[role];
      const response = await axios.get(`${ApiUrl}/${endpoint}/${userId}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.errors || 'Failed to fetch user data');
    }
  },
);

export const updateUserData = createAsyncThunk(
  'user/updateUserData',
  async ({ role, userId, userData }, { rejectWithValue }) => {
    try {
      const endpoint = roleEndpoints[role];

      const response = await axios.put(`${ApiUrl}/${endpoint}/${userId}`, userData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      return response.data;
    } catch (error) {
      // Handle different error formats
      if (error.response?.data?.error) {
        return rejectWithValue(error.response.data.error);
      } if (error.response?.data?.errors) {
        return rejectWithValue(error.response.data.errors);
      } if (error.response?.data) {
        return rejectWithValue(error.response.data);
      }
      return rejectWithValue(error.message || 'Failed to update user data');
    }
  },
);

const userSlice = createSlice({
  name: 'user',
  initialState: {
    userData: null,
    status: 'idle',
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchUserData.pending, (state) => ({
        ...state,
        status: 'loading',
      }))
      .addCase(fetchUserData.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        userData: action.payload,
      }))
      .addCase(fetchUserData.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))
      .addCase(updateUserData.pending, (state) => ({
        ...state,
        status: 'loading',
      }))
      .addCase(updateUserData.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        userData: action.payload,
      }))
      .addCase(updateUserData.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }));
  },
});

export default userSlice.reducer;
