import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { toast } from 'react-toastify';
import ApiUrl from '../../components/helper-functions/ApiUrl';

// Helper function to get auth token
const getAuthToken = () => {
  const authData = localStorage.getItem('authData');
  if (authData) {
    const parsedData = JSON.parse(authData);
    return parsedData.token;
  }
  return null;
};

// Async thunk for fetching dashboard data
export const fetchDashboardData = createAsyncThunk(
  'admin/fetchDashboardData',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/dashboard`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch dashboard data: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for fetching all orders
export const fetchAdminOrders = createAsyncThunk(
  'admin/fetchOrders',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/orders`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch orders: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for fetching all payouts
export const fetchAdminPayouts = createAsyncThunk(
  'admin/fetchPayouts',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/payouts`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch payouts: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for fetching service fees
export const fetchServiceFees = createAsyncThunk(
  'admin/fetchServiceFees',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/service_fees`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch service fees: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for updating service fees
export const updateServiceFees = createAsyncThunk(
  'admin/updateServiceFees',
  async (serviceFees, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.put(
        `${ApiUrl}/admin/service_fees`,
        { service_fee: serviceFees },
        {
          headers: {
            Authorization: token,
          },
        },
      );

      toast.success('Service fees updated successfully');
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to update service fees: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for fetching all vendors
export const fetchVendors = createAsyncThunk(
  'admin/fetchVendors',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/vendors`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch vendors: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for fetching pending vendors
export const fetchPendingVendors = createAsyncThunk(
  'admin/fetchPendingVendors',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/vendors/pending`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch pending vendors: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for approving a vendor
export const approveVendor = createAsyncThunk(
  'admin/approveVendor',
  async (vendorId, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.patch(
        `${ApiUrl}/admin/vendors/${vendorId}/approve`,
        {},
        {
          headers: {
            Authorization: token,
          },
        },
      );

      toast.success('Vendor approved successfully');
      return response.data.vendor;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to approve vendor: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for rejecting a vendor
export const rejectVendor = createAsyncThunk(
  'admin/rejectVendor',
  async (vendorId, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.patch(
        `${ApiUrl}/admin/vendors/${vendorId}/reject`,
        {},
        {
          headers: {
            Authorization: token,
          },
        },
      );

      toast.success('Vendor rejected');
      return response.data.vendor;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to reject vendor: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for fetching all riders
export const fetchRiders = createAsyncThunk(
  'admin/fetchRiders',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/riders`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch riders: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for fetching pending riders
export const fetchPendingRiders = createAsyncThunk(
  'admin/fetchPendingRiders',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/riders/pending`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch pending riders: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for approving a rider
export const approveRider = createAsyncThunk(
  'admin/approveRider',
  async (riderId, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.patch(
        `${ApiUrl}/admin/riders/${riderId}/approve`,
        {},
        {
          headers: {
            Authorization: token,
          },
        },
      );

      toast.success('Rider approved successfully');
      return response.data.rider;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to approve rider: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for rejecting a rider
export const rejectRider = createAsyncThunk(
  'admin/rejectRider',
  async (riderId, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.patch(
        `${ApiUrl}/admin/riders/${riderId}/reject`,
        {},
        {
          headers: {
            Authorization: token,
          },
        },
      );

      toast.success('Rider rejected');
      return response.data.rider;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to reject rider: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// New async thunk for fetching rider statistics
export const fetchRiderStatistics = createAsyncThunk(
  'admin/fetchRiderStatistics',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/riders/statistics`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch rider statistics: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// New async thunk for fetching vendor statistics
export const fetchVendorStatistics = createAsyncThunk(
  'admin/fetchVendorStatistics',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/vendors/statistics`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch vendor statistics: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for fetching all customers
export const fetchCustomers = createAsyncThunk(
  'admin/fetchCustomers',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/customers`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch customers: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for fetching all categories
export const fetchCategories = createAsyncThunk(
  'admin/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/categories`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch categories: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for creating a category
export const createCategory = createAsyncThunk(
  'admin/createCategory',
  async (categoryData, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.post(
        `${ApiUrl}/admin/categories`,
        categoryData,
        {
          headers: {
            Authorization: token,
          },
        },
      );

      toast.success('Category created successfully');
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to create category: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for updating a category
export const updateCategory = createAsyncThunk(
  'admin/updateCategory',
  async ({ id, categoryData }, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.put(
        `${ApiUrl}/admin/categories/${id}`,
        categoryData,
        {
          headers: {
            Authorization: token,
          },
        },
      );

      toast.success('Category updated successfully');
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to update category: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for deleting a category
export const deleteCategory = createAsyncThunk(
  'admin/deleteCategory',
  async (id, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      await axios.delete(`${ApiUrl}/admin/categories/${id}`, {
        headers: {
          Authorization: token,
        },
      });

      toast.success('Category deleted successfully');
      return id;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to delete category: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for fetching all complaints
export const fetchComplaints = createAsyncThunk(
  'admin/fetchComplaints',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/complaints`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch complaints: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for fetching complaint statistics
export const fetchComplaintStatistics = createAsyncThunk(
  'admin/fetchComplaintStatistics',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/complaints/statistics`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch complaint statistics: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for updating a complaint status
export const updateComplaintStatus = createAsyncThunk(
  'admin/updateComplaintStatus',
  async ({ id, status }, { rejectWithValue, dispatch }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.patch(
        `${ApiUrl}/admin/complaints/${id}`,
        { complaint: { status } },
        {
          headers: {
            Authorization: token,
          },
        },
      );

      // Fetch updated statistics after status change
      dispatch(fetchComplaintStatistics());

      toast.success(`Complaint status updated to ${status.replace('_', ' ')}`);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to update complaint status: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for fetching all suggestions
export const fetchSuggestions = createAsyncThunk(
  'admin/fetchSuggestions',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/suggestions`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch suggestions: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for fetching suggestion statistics
export const fetchSuggestionStatistics = createAsyncThunk(
  'admin/fetchSuggestionStatistics',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/suggestions/statistics`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch suggestion statistics: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// Async thunk for updating a suggestion status
export const updateSuggestionStatus = createAsyncThunk(
  'admin/updateSuggestionStatus',
  async ({ id, status }, { rejectWithValue, dispatch }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.patch(
        `${ApiUrl}/admin/suggestions/${id}`,
        { suggestion: { status } },
        {
          headers: {
            Authorization: token,
          },
        },
      );

      // Fetch updated statistics after status change
      dispatch(fetchSuggestionStatistics());

      toast.success(`Suggestion status updated to ${status.replace('_', ' ')}`);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to update suggestion status: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

// New async thunk for fetching customer statistics
export const fetchCustomerStatistics = createAsyncThunk(
  'admin/fetchCustomerStatistics',
  async (_, { rejectWithValue }) => {
    try {
      const token = getAuthToken();
      if (!token) {
        return rejectWithValue('Authentication token not found');
      }

      const response = await axios.get(`${ApiUrl}/admin/customers/statistics`, {
        headers: {
          Authorization: token,
        },
      });

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || err.message;
      toast.error(`Failed to fetch customer statistics: ${errorMessage}`);
      return rejectWithValue(errorMessage);
    }
  },
);

const initialState = {
  dashboardData: null,
  orders: [],
  payouts: [],
  vendors: [],
  pendingVendors: [],
  riders: [],
  pendingRiders: [],
  customers: [],
  categories: [],
  complaints: [],
  suggestions: [],
  complaintStatistics: null,
  suggestionStatistics: null,
  customerStatistics: null,
  riderStatistics: null,
  vendorStatistics: null,
  serviceFees: {
    vendorFeePercentage: 0,
    riderFeePercentage: 0,
    platformFeePercentage: 0,
  },
  activeTab: 'dashboard',
  status: 'idle', // 'idle' | 'loading' | 'succeeded' | 'failed'
  error: null,
};

const adminSlice = createSlice({
  name: 'admin',
  initialState,
  reducers: {
    setActiveTab: (state, action) => ({
      ...state,
      activeTab: action.payload,
    }),
    clearError: (state) => ({
      ...state,
      error: null,
    }),
    setServiceFees: (state, action) => ({
      ...state,
      serviceFees: action.payload,
    }),
  },
  extraReducers: (builder) => {
    builder
      // Dashboard Data
      .addCase(fetchDashboardData.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchDashboardData.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        dashboardData: action.payload,
      }))
      .addCase(fetchDashboardData.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Orders
      .addCase(fetchAdminOrders.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchAdminOrders.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        orders: action.payload,
      }))
      .addCase(fetchAdminOrders.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Payouts
      .addCase(fetchAdminPayouts.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchAdminPayouts.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        payouts: action.payload,
      }))
      .addCase(fetchAdminPayouts.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Service Fees
      .addCase(fetchServiceFees.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchServiceFees.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        serviceFees: action.payload,
      }))
      .addCase(fetchServiceFees.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Update Service Fees
      .addCase(updateServiceFees.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(updateServiceFees.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        serviceFees: action.payload,
      }))
      .addCase(updateServiceFees.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Vendors
      .addCase(fetchVendors.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchVendors.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        vendors: action.payload,
      }))
      .addCase(fetchVendors.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Pending Vendors
      .addCase(fetchPendingVendors.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchPendingVendors.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        pendingVendors: action.payload,
      }))
      .addCase(fetchPendingVendors.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Approve Vendor
      .addCase(approveVendor.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(approveVendor.fulfilled, (state, action) => {
        // Remove from pending vendors and add to vendors
        const updatedPendingVendors = state.pendingVendors.filter(
          (vendor) => vendor.id !== action.payload.id,
        );

        return {
          ...state,
          status: 'succeeded',
          vendors: [...state.vendors, action.payload],
          pendingVendors: updatedPendingVendors,
        };
      })
      .addCase(approveVendor.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Reject Vendor
      .addCase(rejectVendor.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(rejectVendor.fulfilled, (state, action) => {
        // Update the vendor in both lists
        const updatedVendors = state.vendors.map((vendor) => (vendor.id === action.payload.id
          ? action.payload : vendor));
        const updatedPendingVendors = state.pendingVendors.filter(
          (vendor) => vendor.id !== action.payload.id,
        );

        return {
          ...state,
          status: 'succeeded',
          vendors: updatedVendors,
          pendingVendors: updatedPendingVendors,
        };
      })
      .addCase(rejectVendor.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Riders
      .addCase(fetchRiders.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchRiders.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        riders: action.payload,
      }))
      .addCase(fetchRiders.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Pending Riders
      .addCase(fetchPendingRiders.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchPendingRiders.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        pendingRiders: action.payload,
      }))
      .addCase(fetchPendingRiders.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Approve Rider
      .addCase(approveRider.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(approveRider.fulfilled, (state, action) => {
        // Remove from pending riders and add to riders
        const updatedPendingRiders = state.pendingRiders.filter(
          (rider) => rider.id !== action.payload.id,
        );

        return {
          ...state,
          status: 'succeeded',
          riders: [...state.riders, action.payload],
          pendingRiders: updatedPendingRiders,
        };
      })
      .addCase(approveRider.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Reject Rider
      .addCase(rejectRider.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(rejectRider.fulfilled, (state, action) => {
        // Update the rider in both lists
        const updatedRiders = state.riders.map((rider) => (rider.id === action.payload.id
          ? action.payload : rider));
        const updatedPendingRiders = state.pendingRiders.filter(
          (rider) => rider.id !== action.payload.id,
        );

        return {
          ...state,
          status: 'succeeded',
          riders: updatedRiders,
          pendingRiders: updatedPendingRiders,
        };
      })
      .addCase(rejectRider.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Customers
      .addCase(fetchCustomers.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchCustomers.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        customers: action.payload,
      }))
      .addCase(fetchCustomers.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Categories
      .addCase(fetchCategories.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchCategories.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        categories: action.payload,
      }))
      .addCase(fetchCategories.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Create Category
      .addCase(createCategory.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(createCategory.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        categories: [...state.categories, action.payload],
      }))
      .addCase(createCategory.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Update Category
      .addCase(updateCategory.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(updateCategory.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        categories: state.categories.map((category) => (category.id === action.payload.id
          ? action.payload : category)),
      }))
      .addCase(updateCategory.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Delete Category
      .addCase(deleteCategory.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(deleteCategory.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        categories: state.categories.filter((category) => category.id !== action.payload),
      }))
      .addCase(deleteCategory.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))
      // Fetch Complaints
      .addCase(fetchComplaints.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchComplaints.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        complaints: action.payload,
      }))
      .addCase(fetchComplaints.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Update Complaint Status
      .addCase(updateComplaintStatus.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(updateComplaintStatus.fulfilled, (state, action) => {
        // Update the complaint in the complaints array
        const updatedComplaints = state.complaints.map((complaint) => (complaint.id === action
          .payload.id ? action.payload : complaint));

        return {
          ...state,
          status: 'succeeded',
          complaints: updatedComplaints,
        };
      })
      .addCase(updateComplaintStatus.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Complaint Statistics
      .addCase(fetchComplaintStatistics.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchComplaintStatistics.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        complaintStatistics: action.payload,
      }))
      .addCase(fetchComplaintStatistics.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Suggestions
      .addCase(fetchSuggestions.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchSuggestions.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        suggestions: action.payload,
      }))
      .addCase(fetchSuggestions.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Update Suggestion Status
      .addCase(updateSuggestionStatus.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(updateSuggestionStatus.fulfilled, (state, action) => {
        // Update the suggestion in the suggestions array
        const updatedSuggestions = state.suggestions.map((suggestion) => (suggestion.id === action
          .payload.id ? action.payload : suggestion));

        return {
          ...state,
          status: 'succeeded',
          suggestions: updatedSuggestions,
        };
      })
      .addCase(updateSuggestionStatus.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Suggestion Statistics
      .addCase(fetchSuggestionStatistics.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchSuggestionStatistics.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        suggestionStatistics: action.payload,
      }))
      .addCase(fetchSuggestionStatistics.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Customer Statistics
      .addCase(fetchCustomerStatistics.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchCustomerStatistics.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        customerStatistics: action.payload,
      }))
      .addCase(fetchCustomerStatistics.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Rider Statistics
      .addCase(fetchRiderStatistics.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchRiderStatistics.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        riderStatistics: action.payload,
      }))
      .addCase(fetchRiderStatistics.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Vendor Statistics
      .addCase(fetchVendorStatistics.pending, (state) => ({
        ...state,
        status: 'loading',
        error: null,
      }))
      .addCase(fetchVendorStatistics.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        vendorStatistics: action.payload,
      }))
      .addCase(fetchVendorStatistics.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }));
  },
});

export const { setActiveTab, clearError, setServiceFees } = adminSlice.actions;

// Selectors
export const selectDashboardData = (state) => state.admin.dashboardData;
export const selectAdminOrders = (state) => state.admin.orders;
export const selectAdminPayouts = (state) => state.admin.payouts;
export const selectServiceFees = (state) => state.admin.serviceFees;
export const selectVendors = (state) => state.admin.vendors;
export const selectPendingVendors = (state) => state.admin.pendingVendors;
export const selectRiders = (state) => state.admin.riders;
export const selectPendingRiders = (state) => state.admin.pendingRiders;
export const selectCustomers = (state) => state.admin.customers;
export const selectCategories = (state) => state.admin.categories;
export const selectActiveTab = (state) => state.admin.activeTab;
export const selectAdminStatus = (state) => state.admin.status;
export const selectAdminError = (state) => state.admin.error;

// New selectors for complaints and suggestions
export const selectComplaints = (state) => state.admin.complaints;
export const selectSuggestions = (state) => state.admin.suggestions;
export const selectComplaintStatistics = (state) => state.admin.complaintStatistics;
export const selectSuggestionStatistics = (state) => state.admin.suggestionStatistics;

// Selector for customer statistics
export const selectCustomerStatistics = (state) => state.admin.customerStatistics;

// Selector for rider statistics
export const selectRiderStatistics = (state) => state.admin.riderStatistics;

// Selector for vendor statistics
export const selectVendorStatistics = (state) => state.admin.vendorStatistics;

export default adminSlice.reducer;
