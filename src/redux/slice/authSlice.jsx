import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import ApiUrl from '../../components/helper-functions/ApiUrl';
import { getStorageItem, setStorageItem, removeStorageItem } from '../../utils/localStorage';

// Add function to initialize auth state
const loadAuthState = () => {
  try {
    const authData = getStorageItem('authData');
    const expirationDate = getStorageItem('tokenExpirationDate');

    if (authData && expirationDate) {
      if (new Date().getTime() > new Date(expirationDate).getTime()) {
        removeStorageItem('authData');
        removeStorageItem('tokenExpirationDate');

        delete axios.defaults.headers.common.Authorization;

        return {
          user: null,
          role: null,
          isAuthenticated: false,
          loading: false,
          error: null,
          forgotPasswordLoading: false,
          forgotPasswordSuccess: false,
          resetPasswordLoading: false,
          resetPasswordSuccess: false,
        };
      }

      if (authData.token) {
        axios.defaults.headers.common.Authorization = authData.token;
      }
      return {
        user: authData.data,
        role: authData.data.role,
        isAuthenticated: true,
        loading: false,
        error: null,
        forgotPasswordLoading: false,
        forgotPasswordSuccess: false,
        resetPasswordLoading: false,
        resetPasswordSuccess: false,
      };
    }
  } catch (error) {
    throw new Error('Error loading auth state:', error);
  }

  return {
    user: null,
    role: null,
    loading: false,
    error: null,
    isAuthenticated: false,
    forgotPasswordLoading: false,
    forgotPasswordSuccess: false,
    resetPasswordLoading: false,
    resetPasswordSuccess: false,
  };
};

export const login = createAsyncThunk('auth/login', async ({ role, credentials }, { rejectWithValue }) => {
  try {
    const response = await axios.post(`${ApiUrl}/${role}/login`, { [role]: credentials });
    const token = response.headers.authorization;
    if (response.data && token) {
      const expirationDate = new Date();
      expirationDate.setMonth(expirationDate.getMonth() + 1);

      // Save the entire response object in local storage
      setStorageItem('authData', { ...response.data, token });
      setStorageItem('tokenExpirationDate', expirationDate.toISOString());

      axios.defaults.headers.common.Authorization = token;
    }
    return response.data;
  } catch (error) {
    return rejectWithValue(error.response.data);
  }
});

export const signup = createAsyncThunk(
  'auth/signup',
  async ({ role, userData }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${ApiUrl}/${role}/signup`, userData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.errors || error.response?.data || 'Sign-up failed');
    }
  },
);

export const logout = createAsyncThunk('auth/logout', async (role, { rejectWithValue }) => {
  try {
    await axios.delete(`${ApiUrl}/${role}/logout`);
    removeStorageItem('authData');
    removeStorageItem('tokenExpirationDate');
    delete axios.defaults.headers.common.Authorization;
    return { success: true };
  } catch (error) {
    return rejectWithValue(error.response.data);
  }
});

// Forgot Password - Request reset token
export const forgotPassword = createAsyncThunk(
  'auth/forgotPassword',
  async ({ role, email }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${ApiUrl}/${role}/password`, {
        [role]: { email }
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.errors || error.response?.data || 'Failed to send reset email');
    }
  }
);

// Reset Password - Submit new password with token
export const resetPassword = createAsyncThunk(
  'auth/resetPassword',
  async ({ role, resetToken, password, passwordConfirmation }, { rejectWithValue }) => {
    try {
      const response = await axios.put(`${ApiUrl}/${role}/password`, {
        [role]: {
          reset_password_token: resetToken,
          password,
          password_confirmation: passwordConfirmation
        }
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.errors || error.response?.data || 'Failed to reset password');
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState: loadAuthState(),
  reducers: {
    clearError(state) {
      return {
        ...state,
        error: null,
      };
    },
    clearSuccess(state) {
      return {
        ...state,
        success: false,
      };
    },
    clearForgotPasswordState(state) {
      return {
        ...state,
        forgotPasswordLoading: false,
        forgotPasswordSuccess: false,
        error: null,
      };
    },
    clearResetPasswordState(state) {
      return {
        ...state,
        resetPasswordLoading: false,
        resetPasswordSuccess: false,
        error: null,
      };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(login.pending, (state) => ({ ...state, loading: true, error: null }))
      .addCase(login.fulfilled, (state, action) => ({
        ...state,
        loading: false,
        user: action.payload.data,
        role: action.payload.data.role,
        error: null,
        isAuthenticated: true,
      }))
      .addCase(login.rejected, (state, action) => ({
        ...state,
        loading: false,
        error: action.error.message,
        isAuthenticated: false,
      }))
      .addCase(signup.pending, (state) => ({
        ...state,
        loading: true,
        error: null,
        success: false,
        isAuthenticated: false,
      }))
      .addCase(signup.fulfilled, (state, action) => ({
        ...state,
        loading: false,
        user: action.payload.data,
        role: action.payload.role,
        error: null,
        success: true,
        isAuthenticated: false,
      }))
      .addCase(signup.rejected, (state, action) => ({
        ...state,
        loading: false,
        error: action.payload || action.error.message,
        success: false,
        isAuthenticated: false,
      }))
      .addCase(logout.fulfilled, (state) => ({
        ...state,
        user: null,
        role: null,
        isAuthenticated: false,
      }))
      // Forgot Password cases
      .addCase(forgotPassword.pending, (state) => ({
        ...state,
        forgotPasswordLoading: true,
        error: null,
        forgotPasswordSuccess: false,
      }))
      .addCase(forgotPassword.fulfilled, (state) => ({
        ...state,
        forgotPasswordLoading: false,
        forgotPasswordSuccess: true,
        error: null,
      }))
      .addCase(forgotPassword.rejected, (state, action) => ({
        ...state,
        forgotPasswordLoading: false,
        forgotPasswordSuccess: false,
        error: action.payload || action.error.message,
      }))
      // Reset Password cases
      .addCase(resetPassword.pending, (state) => ({
        ...state,
        resetPasswordLoading: true,
        error: null,
        resetPasswordSuccess: false,
      }))
      .addCase(resetPassword.fulfilled, (state) => ({
        ...state,
        resetPasswordLoading: false,
        resetPasswordSuccess: true,
        error: null,
      }))
      .addCase(resetPassword.rejected, (state, action) => ({
        ...state,
        resetPasswordLoading: false,
        resetPasswordSuccess: false,
        error: action.payload || action.error.message,
      }));
  },
});

export const {
  setAuthData,
  clearError,
  clearSuccess,
  clearForgotPasswordState,
  clearResetPasswordState
} = authSlice.actions;
export default authSlice.reducer;
