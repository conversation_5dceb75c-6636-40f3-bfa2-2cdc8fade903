import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import ApiUrl from '../../components/helper-functions/ApiUrl';

export const createOrder = createAsyncThunk(
  'orders/createOrder',
  async ({
    customerId, riderId, foodIds, quantities, priceIndices, deliveryAddress,
    deliveryPrice, donation, recipientAttributes,
  }, { rejectWithValue }) => {
    try {
      const orderData = {
        customer_id: customerId,
        rider_id: riderId,
        food_ids: foodIds,
        quantities,
        delivery_address: deliveryAddress,
        delivery_price: deliveryPrice,
        donation,
        recipient_attributes: recipientAttributes || undefined,
      };

      // Add price indices if provided
      if (priceIndices && priceIndices.length > 0) {
        orderData.price_indices = priceIndices;
      }

      const response = await axios.post(`${ApiUrl}/customers/${customerId}/orders`, {
        order: orderData,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  },
);

export const updateOrderStatus = createAsyncThunk(
  'orders/updateOrderStatus',
  async ({
    orderId, status, vendorId, riderId, customerId,
  }, { rejectWithValue }) => {
    try {
      let url = '';
      const data = { order: { status } };

      if (vendorId) {
        url = `${ApiUrl}/vendors/${vendorId}/orders/${orderId}`;
      } else if (riderId) {
        url = `${ApiUrl}/riders/${riderId}/orders/${orderId}`;
      } else if (customerId) {
        url = `${ApiUrl}/customers/${customerId}/orders/${orderId}`;
      } else {
        throw new Error('Invalid parameters: vendorId, riderId or customerId must be provided');
      }

      const response = await axios.patch(url, data);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  },
);

export const fetchOrders = createAsyncThunk(
  'orders/fetchOrders',
  async ({ riderId, vendorId, customerId }, { rejectWithValue }) => {
    try {
      let url = '';
      if (riderId) {
        url = `${ApiUrl}/riders/${riderId}/orders`;
      } else if (vendorId) {
        url = `${ApiUrl}/vendors/${vendorId}/orders`;
      } else if (customerId) {
        url = `${ApiUrl}/customers/${customerId}/orders`;
      } else {
        throw new Error('Invalid parameters: riderId, customerId or vendorId must be provided');
      }

      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  },
);

export const confirmDelivery = createAsyncThunk(
  'orders/confirmDelivery',
  async ({ orderId, customerId }, { rejectWithValue }) => {
    try {
      // Get the authentication token from localStorage
      const authData = localStorage.getItem('authData');
      if (!authData) {
        return rejectWithValue('Authentication required. Please log in.');
      }

      const { token } = JSON.parse(authData);
      if (!token) {
        return rejectWithValue('Authentication token not found. Please log in again.');
      }

      // Create a new instance of axios with the token in the headers
      // Make sure the token is in the format expected by the backend (Bearer token)
      // Include customerId in the URL if provided
      const url = customerId
        ? `${ApiUrl}/customers/${customerId}/orders/${orderId}/confirm_delivery`
        : `${ApiUrl}/orders/${orderId}/confirm_delivery`;

      const response = await axios.post(
        url,
        {},
        {
          headers: {
            Authorization: token.startsWith('Bearer') ? token : `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  },
);

export const fetchCustomerStatistics = createAsyncThunk(
  'orders/fetchCustomerStatistics',
  async ({ customerId, page = 1, perPage = 10 }, { rejectWithValue }) => {
    try {
      // Get the authentication token from localStorage
      const authData = localStorage.getItem('authData');
      if (!authData) {
        return rejectWithValue('Authentication required. Please log in.');
      }

      const { token } = JSON.parse(authData);
      if (!token) {
        return rejectWithValue('Authentication token not found. Please log in again.');
      }

      // Create the URL with pagination parameters
      const url = `${ApiUrl}/customers/${customerId}/statistics?page=${page}&per_page=${perPage}`;

      const response = await axios.get(
        url,
        {
          headers: {
            Authorization: token.startsWith('Bearer') ? token : `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  },
);

const ordersSlice = createSlice({
  name: 'orders',
  initialState: {
    orders: [],
    status: 'idle',
    error: null,
    statistics: null,
    orderHistory: {
      orders: [],
      pagination: {
        currentPage: 1,
        perPage: 10,
        totalPages: 0,
        totalOrders: 0,
      },
    },
    statisticsStatus: 'idle',
    statisticsError: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Create Order
      .addCase(createOrder.pending, (state) => ({
        ...state,
        status: 'loading',
      }))
      .addCase(createOrder.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        orders: [...state.orders, action.payload],
      }))
      .addCase(createOrder.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Orders
      .addCase(fetchOrders.pending, (state) => ({
        ...state,
        status: 'loading',
      }))
      .addCase(fetchOrders.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        orders: action.payload,
      }))
      .addCase(fetchOrders.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Update Order Status
      .addCase(updateOrderStatus.pending, (state) => ({
        ...state,
        status: 'loading',
      }))
      .addCase(updateOrderStatus.fulfilled, (state, action) => {
        const updatedOrders = state.orders.map((order) => (order.id === action
          .payload.id ? action.payload : order));
        return {
          ...state,
          status: 'succeeded',
          orders: updatedOrders,
        };
      })
      .addCase(updateOrderStatus.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Confirm Delivery
      .addCase(confirmDelivery.pending, (state) => ({
        ...state,
        status: 'loading',
      }))
      .addCase(confirmDelivery.fulfilled, (state, action) => {
        const updatedOrders = state.orders.map((order) => (
          order.id === action.payload.order.id ? action.payload.order : order
        ));
        return {
          ...state,
          status: 'succeeded',
          orders: updatedOrders,
        };
      })
      .addCase(confirmDelivery.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))

      // Fetch Customer Statistics
      .addCase(fetchCustomerStatistics.pending, (state) => ({
        ...state,
        statisticsStatus: 'loading',
      }))
      .addCase(fetchCustomerStatistics.fulfilled, (state, action) => ({
        ...state,
        statisticsStatus: 'succeeded',
        statistics: action.payload.statistics,
        orderHistory: {
          orders: action.payload.order_history.orders,
          pagination: {
            currentPage: action.payload.order_history.pagination.current_page,
            perPage: action.payload.order_history.pagination.per_page,
            totalPages: action.payload.order_history.pagination.total_pages,
            totalOrders: action.payload.order_history.pagination.total_orders,
          },
        },
      }))
      .addCase(fetchCustomerStatistics.rejected, (state, action) => ({
        ...state,
        statisticsStatus: 'failed',
        statisticsError: action.payload,
      }));
  },
});

export default ordersSlice.reducer;

// Selectors for regular orders
export const selectOrders = (state) => state.orders.orders;
export const selectOrdersStatus = (state) => state.orders.status;
export const selectOrdersError = (state) => state.orders.error;

// Selectors for statistics and order history
export const selectStatistics = (state) => state.orders.statistics;
export const selectOrderHistory = (state) => state.orders.orderHistory;
export const selectStatisticsStatus = (state) => state.orders.statisticsStatus;
export const selectStatisticsError = (state) => state.orders.statisticsError;
export const selectPagination = (state) => state.orders.orderHistory.pagination;
