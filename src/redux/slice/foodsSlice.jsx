import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import ApiUrl from '../../components/helper-functions/ApiUrl';

export const addFood = createAsyncThunk('foods/addFood', async ({ vendorId, foodData }, { rejectWithValue }) => {
  try {
    const response = await axios.post(`${ApiUrl}/vendors/${vendorId}/foods`, foodData);
    return response.data;
  } catch (error) {
    return rejectWithValue(error.response?.data || 'Failed to add food');
  }
});

export const fetchFoods = createAsyncThunk(
  'foods/fetchFoods',
  async (vendorId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${ApiUrl}/vendors/${vendorId}/foods`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch foods');
    }
  },
);

// Update a food (name, description, and prices)
export const updateFood = createAsyncThunk(
  'foods/updateFood',
  async ({ vendorId, foodId, formData }, { rejectWithValue }) => {
    try {
      const response = await axios.patch(`${ApiUrl}/vendors/${vendorId}/foods/${foodId}`, formData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to update food');
    }
  }
);

// Delete a food
export const deleteFood = createAsyncThunk(
  'foods/deleteFood',
  async ({ vendorId, foodId }, { rejectWithValue }) => {
    try {
      await axios.delete(`${ApiUrl}/vendors/${vendorId}/foods/${foodId}`);
      return { foodId };
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to delete food');
    }
  }
);

const foodsSlice = createSlice({
  name: 'foods',
  initialState: {
    foods: [],
    status: 'idle',
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchFoods.pending, (state) => ({
        ...state,
        status: 'loading',
      }))
      .addCase(fetchFoods.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        // Ensure backward compatibility with new price structure
        foods: action.payload.map(food => ({
          ...food,
          // Ensure prices array exists
          prices: food.prices || [food.price],
          has_multiple_prices: food.has_multiple_prices || false,
          min_price: food.min_price || food.price,
          max_price: food.max_price || food.price
        })),
      }))
      .addCase(fetchFoods.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))
      .addCase(addFood.pending, (state) => ({
        ...state,
        status: 'loading',
      }))
      .addCase(addFood.fulfilled, (state, action) => {
        const newFood = {
          ...action.payload,
          // Ensure prices array exists for new food
          prices: action.payload.prices || [action.payload.price],
          has_multiple_prices: action.payload.has_multiple_prices || false,
          min_price: action.payload.min_price || action.payload.price,
          max_price: action.payload.max_price || action.payload.price
        };

        return {
          ...state,
          status: 'succeeded',
          foods: [...state.foods, newFood],
        };
      })
      .addCase(addFood.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))
      // Update food handlers
      .addCase(updateFood.fulfilled, (state, action) => {
        const updated = {
          ...action.payload,
          prices: action.payload.prices || [action.payload.price],
          has_multiple_prices: action.payload.has_multiple_prices || false,
          min_price: action.payload.min_price || action.payload.price,
          max_price: action.payload.max_price || action.payload.price
        };
        const idx = state.foods.findIndex(f => f.id === updated.id);
        if (idx !== -1) {
          const newFoods = [...state.foods];
          newFoods[idx] = updated;
          return { ...state, foods: newFoods };
        }
        return { ...state, foods: [...state.foods, updated] };
      })
      .addCase(updateFood.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }))
      // Delete food handlers
      .addCase(deleteFood.fulfilled, (state, action) => ({
        ...state,
        foods: state.foods.filter(f => f.id !== action.payload.foodId),
      }))
      .addCase(deleteFood.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.payload,
      }));
  },
});

export default foodsSlice.reducer;

export const selectFoods = (state) => state.foods.foods;
export const selectFoodsStatus = (state) => state.foods.status;
export const selectFoodsError = (state) => state.foods.error;
