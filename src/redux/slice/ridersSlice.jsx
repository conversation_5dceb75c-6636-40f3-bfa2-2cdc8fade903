import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import ApiUrl from '../../components/helper-functions/ApiUrl';

export const fetchRiders = createAsyncThunk('riders/fetchRiders', async () => {
  const response = await axios.get(`${ApiUrl}/riders`);
  return response.data;
});

export const fetchRiderStatistics = createAsyncThunk(
  'riders/fetchRiderStatistics',
  async (riderId, { rejectWithValue }) => {
    try {
      // Get the authentication token from localStorage
      const authData = localStorage.getItem('authData');
      if (!authData) {
        return rejectWithValue('Authentication required. Please log in.');
      }

      const { token } = JSON.parse(authData);
      if (!token) {
        return rejectWithValue('Authentication token not found. Please log in again.');
      }

      // Create a new instance of axios with the token in the headers
      const response = await axios.get(
        `${ApiUrl}/riders/${riderId}/statistics`,
        {
          headers: {
            Authorization: token.startsWith('Bearer') ? token : `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  },
);

const ridersSlice = createSlice({
  name: 'riders',
  initialState: {
    riders: [],
    statistics: null,
    status: 'idle',
    statisticsStatus: 'idle',
    loading: false,
    error: null,
    statisticsError: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchRiders.pending, (state) => ({
        ...state,
        status: 'loading',
      }))
      .addCase(fetchRiders.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        riders: action.payload,
      }))
      .addCase(fetchRiders.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.error.message,
      }))
      .addCase(fetchRiderStatistics.pending, (state) => ({
        ...state,
        statisticsStatus: 'loading',
      }))
      .addCase(fetchRiderStatistics.fulfilled, (state, action) => ({
        ...state,
        statisticsStatus: 'succeeded',
        statistics: action.payload,
      }))
      .addCase(fetchRiderStatistics.rejected, (state, action) => ({
        ...state,
        statisticsStatus: 'failed',
        statisticsError: action.payload,
      }));
  },
});

export default ridersSlice.reducer;

export const selectRiders = (state) => state.riders.riders;
export const selectRidersStatus = (state) => state.riders.status;
export const selectRidersError = (state) => state.riders.error;
export const selectRiderStatistics = (state) => state.riders.statistics;
export const selectRiderStatisticsStatus = (state) => state.riders.statisticsStatus;
export const selectRiderStatisticsError = (state) => state.riders.statisticsError;
