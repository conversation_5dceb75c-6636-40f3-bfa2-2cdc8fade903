import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import ApiUrl from '../../components/helper-functions/ApiUrl';

export const fetchVendors = createAsyncThunk('vendors/fetchVendors', async () => {
  const response = await axios.get(`${ApiUrl}/vendors`);
  return response.data;
});

export const fetchVendorStatistics = createAsyncThunk(
  'vendors/fetchVendorStatistics',
  async (vendorId, { rejectWithValue }) => {
    try {
      // Get the authentication token from localStorage
      const authData = localStorage.getItem('authData');
      if (!authData) {
        return rejectWithValue('Authentication required. Please log in.');
      }

      const { token } = JSON.parse(authData);
      if (!token) {
        return rejectWithValue('Authentication token not found. Please log in again.');
      }

      // Create a new instance of axios with the token in the headers
      const response = await axios.get(
        `${ApiUrl}/vendors/${vendorId}/statistics`,
        {
          headers: {
            Authorization: token.startsWith('Bearer') ? token : `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  },
);

// New async thunk for updating vendor availability
export const updateVendorAvailability = createAsyncThunk(
  'vendors/updateVendorAvailability',
  async ({ vendorId, isAvailable }, { rejectWithValue }) => {
    try {
      // Get the authentication token from localStorage
      const authData = localStorage.getItem('authData');
      if (!authData) {
        return rejectWithValue('Authentication required. Please log in.');
      }

      const { token } = JSON.parse(authData);
      if (!token) {
        return rejectWithValue('Authentication token not found. Please log in again.');
      }

      const response = await axios.patch(
        `${ApiUrl}/vendors/${vendorId}/availability`,
        { is_available: isAvailable },
        {
          headers: {
            Authorization: token.startsWith('Bearer') ? token : `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  },
);

const vendorsSlice = createSlice({
  name: 'vendors',
  initialState: {
    vendors: [],
    statistics: null,
    status: 'idle',
    statisticsStatus: 'idle',
    availabilityStatus: 'idle',
    loading: false,
    error: null,
    statisticsError: null,
    availabilityError: null,
  },
  reducers: {
    setCurrentVendor: (state, action) => ({
      ...state,
      currentVendor: action.payload,
    }),
    clearAvailabilityError: (state) => ({
      ...state,
      availabilityError: null,
    }),
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchVendors.pending, (state) => ({
        ...state,
        status: 'loading',
      }))
      .addCase(fetchVendors.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        vendors: action.payload,
      }))
      .addCase(fetchVendors.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.error.message,
      }))
      .addCase(fetchVendorStatistics.pending, (state) => ({
        ...state,
        statisticsStatus: 'loading',
      }))
      .addCase(fetchVendorStatistics.fulfilled, (state, action) => ({
        ...state,
        statisticsStatus: 'succeeded',
        statistics: action.payload,
      }))
      .addCase(fetchVendorStatistics.rejected, (state, action) => ({
        ...state,
        statisticsStatus: 'failed',
        statisticsError: action.payload,
      }))
      // Handle updateVendorAvailability
      .addCase(updateVendorAvailability.pending, (state) => ({
        ...state,
        availabilityStatus: 'loading',
        availabilityError: null,
      }))
      .addCase(updateVendorAvailability.fulfilled, (state, action) => ({
        ...state,
        availabilityStatus: 'succeeded',
        currentVendor: action.payload,
        // Update the vendor in the vendors array if it exists
        vendors: state.vendors.map((vendor) => (vendor.id === action.payload.id
          ? { ...vendor, is_available: action.payload.is_available }
          : vendor)),
      }))
      .addCase(updateVendorAvailability.rejected, (state, action) => ({
        ...state,
        availabilityStatus: 'failed',
        availabilityError: action.payload,
      }));
  },
});

export const { setCurrentVendor, clearAvailabilityError } = vendorsSlice.actions;

export default vendorsSlice.reducer;

export const selectVendors = (state) => state.vendors.vendors;
export const selectVendorsStatus = (state) => state.vendors.status;
export const selectVendorsError = (state) => state.vendors.error;
export const selectVendorStatistics = (state) => state.vendors.statistics;
export const selectVendorStatisticsStatus = (state) => state.vendors.statisticsStatus;
export const selectVendorStatisticsError = (state) => state.vendors.statisticsError;
export const selectCurrentVendor = (state) => state.vendors.currentVendor;
export const selectAvailabilityStatus = (state) => state.vendors.availabilityStatus;
export const selectAvailabilityError = (state) => state.vendors.availabilityError;
