import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import App from './App';
import store from './redux/store';
import PWAInstallPrompt from './components/pwa/PWAInstallPrompt';
import PWAUpdateNotification from './components/pwa/PWAUpdateNotification';
import PWABadgeManager from './components/notifications/PWABadgeManager';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <App />
      <PWAInstallPrompt />
      <PWAUpdateNotification />
      <PWABadgeManager />
    </Provider>
  </React.StrictMode>,
);
