import './App.css';
import PropTypes from 'prop-types';
import axios from 'axios';
import {
  BrowserRouter, Route, Routes, useLocation,
} from 'react-router-dom';
import { useEffect, lazy, Suspense } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import useLocalStorage from './hooks/useLocalStorage';
import ToastProvider from './components/notifications/ToastProvider';
import Header from './components/Header';
import IOSInstallBanner from './components/pwa/IOSInstallBanner';
import ProtectedRoute from './components/helper-functions/ProtectedRoute';
import IconsMenu from './components/IconsMenu';
import LocationAlert from './components/helper-functions/LocationAlert';
import { initializePushNotifications } from './components/notifications/pushNotifications';
import Loader from './components/helper-functions/Loader';

// Lazy load components
const LoginForm = lazy(() => import('./components/auths/LoginForm'));
const SignupForm = lazy(() => import('./components/auths/SignupForm'));
const ForgotPasswordForm = lazy(() => import('./components/auths/ForgotPasswordForm'));
const ResetPasswordForm = lazy(() => import('./components/auths/ResetPasswordForm'));
const HomeDashboard = lazy(() => import('./components/main_dashboard/HomeDashboard'));
const FoodDashboard = lazy(() => import('./components/main_dashboard/FoodDashboard'));
const Cart = lazy(() => import('./components/Cart'));
const RidersDashboard = lazy(() => import('./components/main_dashboard/RidersDashboard'));
const VendorsDashboard = lazy(() => import('./components/main_dashboard/VendorsDashboard'));
const Profile = lazy(() => import('./components/customers/Profile'));
const CustomerOrders = lazy(() => import('./components/customers/CustomerOrders'));
const AdminDashboard = lazy(() => import('./components/admin/AdminDashboard'));
const ContactPage = lazy(() => import('./components/ContactPage'));
const SuggestionsPage = lazy(() => import('./components/SuggestionsPage'));
const FileComplaintPage = lazy(() => import('./components/customers/FileComplaintPage'));
const SupportPage = lazy(() => import('./components/SupportPage'));

const AuthRoute = ({ Component, type }) => {
  const location = useLocation();
  const role = location.state?.role;

  return (
    <>
      {type === 'signup' && <LocationAlert />}
      <Component role={role} type={type} />
    </>
  );
};

// Component to conditionally render Header.
const HeaderWrapper = () => {
  const location = useLocation();
  const authPaths = ['/login', '/signup', '/forgot-password', '/reset-password'];

  // Don't render Header on auth routes.
  if (authPaths.includes(location.pathname)) {
    return null;
  }

  return <Header />;
};

const App = () => {
  const dispatch = useDispatch();
  const { isAuthenticated } = useSelector((state) => state.auth);
  const [authData] = useLocalStorage('authData', null);

  useEffect(() => {
    // Check if token exists and is valid.
    if (authData && authData.token) {
      axios.defaults.headers.common.Authorization = authData.token;
    }
  }, [authData, dispatch]);

  // Initialize push notifications when user is authenticated
  useEffect(() => {
    if (isAuthenticated && authData && authData.token && authData.token !== 'undefined') {
      // Initialize push notifications silently
      // We don't show success messages to avoid disrupting the user experience
      initializePushNotifications(authData.token)
        .catch(() => {
          // Silent fail - no need to show error to user for background operations
          // In production, you might want to log this to an error tracking service
        });
    }
  }, [isAuthenticated, authData]);

  return (
    <BrowserRouter>
      <HeaderWrapper />
      <ToastProvider />
      <Routes>
        <Route
          path="/signup"
          element={(
            <Suspense fallback={<Loader />}>
              <AuthRoute Component={SignupForm} type="signup" />
            </Suspense>
        )}
        />
        <Route
          path="/login"
          element={(
            <Suspense fallback={<Loader />}>
              <AuthRoute Component={LoginForm} type="login" />
            </Suspense>
        )}
        />
        <Route
          path="/forgot-password"
          element={(
            <Suspense fallback={<Loader />}>
              <AuthRoute Component={ForgotPasswordForm} type="forgot-password" />
            </Suspense>
        )}
        />
        <Route
          path="/reset-password"
          element={(
            <Suspense fallback={<Loader />}>
              <AuthRoute Component={ResetPasswordForm} type="reset-password" />
            </Suspense>
        )}
        />
        <Route
          path="/"
          element={(
            <Suspense fallback={<Loader />}>
              <HomeDashboard />
            </Suspense>
        )}
        />
        <Route
          path="/food-dashboard/:vendorId"
          element={(
            <Suspense fallback={<Loader />}>
              <FoodDashboard />
            </Suspense>
        )}
        />
        <Route
          path="/contact"
          element={(
            <Suspense fallback={<Loader />}>
              <ContactPage />
            </Suspense>
        )}
        />
        <Route
          path="/suggestions"
          element={(
            <Suspense fallback={<Loader />}>
              <SuggestionsPage />
            </Suspense>
        )}
        />
        <Route
          path="/support"
          element={(
            <Suspense fallback={<Loader />}>
              <SupportPage />
            </Suspense>
        )}
        />
        <Route element={<ProtectedRoute />}>
          <Route
            path="/cart"
            element={(
              <Suspense fallback={<Loader />}>
                <Cart />
              </Suspense>
          )}
          />
          <Route
            path="/riders-dashboard"
            element={(
              <Suspense fallback={<Loader />}>
                <RidersDashboard />
              </Suspense>
          )}
          />
          <Route
            path="/vendors-dashboard"
            element={(
              <Suspense fallback={<Loader />}>
                <VendorsDashboard />
              </Suspense>
          )}
          />
          <Route
            path="/profile"
            element={(
              <Suspense fallback={<Loader />}>
                <Profile />
              </Suspense>
          )}
          />
          <Route
            path="/customer-orders"
            element={(
              <Suspense fallback={<Loader />}>
                <CustomerOrders />
              </Suspense>
          )}
          />
          <Route
            path="/customer-file-complain"
            element={(
              <Suspense fallback={<Loader />}>
                <FileComplaintPage />
              </Suspense>
          )}
          />
          <Route
            path="/admin-dashboard"
            element={(
              <Suspense fallback={<Loader />}>
                <AdminDashboard />
              </Suspense>
          )}
          />
        </Route>
      </Routes>
      <IOSInstallBanner />
      <IconsMenu />
    </BrowserRouter>
  );
};

AuthRoute.propTypes = {
  Component: PropTypes.elementType.isRequired,
  type: PropTypes.string.isRequired,
};

export default App;
