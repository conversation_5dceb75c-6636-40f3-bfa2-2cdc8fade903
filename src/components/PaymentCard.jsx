import PropTypes from 'prop-types';
import { ShoppingBag, Truck, Gift } from 'lucide-react';

const PaymentCard = ({
  totalPrice, foodPrice, deliveryFee, donate, setDonate, triggerPayment, disabled = false,
}) => {
  const donationAmount = donate ? 1 : 0;

  return (
    <div className="payment-card">
      <div className="payment-summary">
        <div className="payment-item">
          <div className="payment-item-label">
            <ShoppingBag size={16} />
            <span>Food Price:</span>
          </div>
          <div className="payment-item-value">
            ₵
            {foodPrice.toFixed(2)}
          </div>
        </div>

        <div className="payment-item">
          <div className="payment-item-label">
            <Truck size={16} />
            <span>Delivery Fee:</span>
          </div>
          <div className="payment-item-value">
            ₵
            {deliveryFee.toFixed(2)}
          </div>
        </div>

        {donate && (
          <div className="payment-item donation-item">
            <div className="payment-item-label">
              <Gift size={16} />
              <span>Donation:</span>
            </div>
            <div className="payment-item-value">
              ₵
              {donationAmount.toFixed(2)}
            </div>
          </div>
        )}

        <div className="payment-divider" />

        <div className="payment-item total-item">
          <div className="payment-item-label">
            <span>Total Amount:</span>
          </div>
          <div className="payment-item-value total-value">
            ₵
            {totalPrice.toFixed(2)}
          </div>
        </div>
      </div>

      <div className="toggle-container">
        <label htmlFor="donate-checkbox">
          <input
            type="checkbox"
            id="donate-checkbox"
            checked={donate}
            onChange={() => setDonate(!donate)}
          />
          Donate 1 cedis to help maintain the system
        </label>
      </div>

      <button
        type="button"
        onClick={triggerPayment}
        disabled={disabled}
        className="payment-button"
      >
        Proceed to Payment
      </button>
    </div>
  );
};

PaymentCard.propTypes = {
  totalPrice: PropTypes.number.isRequired,
  foodPrice: PropTypes.number.isRequired,
  deliveryFee: PropTypes.number.isRequired,
  donate: PropTypes.bool.isRequired,
  setDonate: PropTypes.func.isRequired,
  triggerPayment: PropTypes.func.isRequired,
  // I am using default parameters instead of defaultProps
  // eslint-disable-next-line react/require-default-props
  disabled: PropTypes.bool,
};

export default PaymentCard;
