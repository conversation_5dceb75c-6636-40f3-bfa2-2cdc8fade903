import React, { useState } from 'react';
import { toast } from 'react-toastify';
import {
  Phone,
  Mail,
  MessageSquare,
  Send,
  User,
  HelpCircle,
  Headphones,
} from 'lucide-react';

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
  });

  const [formErrors, setFormErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.subject.trim()) {
      errors.subject = 'Subject is required';
    }

    if (!formData.message.trim()) {
      errors.message = 'Message is required';
    } else if (formData.message.trim().length < 10) {
      errors.message = 'Message should be at least 10 characters';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      // In a real application, I would send this data to your backend or third party services
      toast.success('Form submitted:', formData);

      // Show success message
      toast.success('Your message has been sent successfully! We will get back to you soon.');

      // Reset form
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: '',
      });
    }
  };

  return (
    <div className="contact-page">
      <div className="contact-header">
        <h1>Contact Us</h1>
        <p>
          We&apos;d love to hear from you! Please fill out the
          form below or use our contact information.
        </p>
      </div>

      <div className="contact-container">
        <div className="contact-info">
          <div className="contact-card">
            <h2>
              <Phone className="contact-icon" />
              General Inquiries
            </h2>
            <p>For general questions about our services, pricing, or feedback:</p>
            <ul className="contact-list">
              <li>
                <User size={16} className="list-icon" />
                <span>Vincent Darkoh</span>
              </li>
              <li>
                <Phone size={16} className="list-icon" />
                <a href="tel:+233240095360">0240095360</a>
              </li>
              <li>
                <Mail size={16} className="list-icon" />
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </li>
              <li className="contact-divider" />
              <li>
                <User size={16} className="list-icon" />
                <span>John Yakubu</span>
              </li>
              <li>
                <Phone size={16} className="list-icon" />
                <a href="tel:+233543020846">0543020846</a>
              </li>
              <li>
                <Mail size={16} className="list-icon" />
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </li>
            </ul>
          </div>

          <div className="contact-card">
            <h2>
              <Headphones className="contact-icon" />
              Technical Support
            </h2>
            <p>Having issues with the app or need technical assistance?</p>
            <ul className="contact-list">
              <li>
                <User size={16} className="list-icon" />
                <span>Collins Bawa</span>
              </li>
              <li>
                <Phone size={16} className="list-icon" />
                <a href="tel:+233546006938">0546006938</a>
              </li>
              <li>
                <Mail size={16} className="list-icon" />
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </li>
            </ul>
          </div>

          <div className="contact-card">
            <h2>
              <MessageSquare className="contact-icon" />
              Business Hours
            </h2>
            <p>We&apos;re available to assist you:</p>
            <ul className="contact-list no-links">
              <li>Monday - Friday: 8:00 AM - 8:00 PM</li>
              <li>Saturday: 9:00 AM - 6:00 PM</li>
              <li>Sunday: 10:00 AM - 4:00 PM</li>
            </ul>
          </div>
        </div>

        <div className="contact-form-container">
          <h2>
            <Send className="contact-icon" />
            Send Us a Message
          </h2>
          <form className="contact-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="name">
                <div>
                  <User size={16} className="form-icon" />
                  Full Name
                  <span className="required">*</span>
                </div>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Your full name"
                />
                {formErrors.name && <p className="error-message">{formErrors.name}</p>}
              </label>
            </div>

            <div className="form-group">
              <label htmlFor="email">
                <div>
                  <Mail size={16} className="form-icon" />
                  Email Address
                  <span className="required">*</span>
                </div>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                />
                {formErrors.email && <p className="error-message">{formErrors.email}</p>}
              </label>
            </div>

            <div className="form-group">
              <label htmlFor="subject">
                <div>
                  <HelpCircle size={16} className="form-icon" />
                  Subject
                  <span className="required">*</span>
                </div>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  placeholder="What is your message about?"
                />
                {formErrors.subject && <p className="error-message">{formErrors.subject}</p>}
              </label>
            </div>

            <div className="form-group">
              <label htmlFor="message">
                <div>
                  <MessageSquare size={16} className="form-icon" />
                  Message
                  <span className="required">*</span>
                </div>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  placeholder="Please describe your inquiry in detail..."
                  rows="5"
                />
                {formErrors.message && <p className="error-message">{formErrors.message}</p>}
              </label>
            </div>

            <button type="submit" className="submit-button">
              <Send size={16} />
              Send Message
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ContactPage;
