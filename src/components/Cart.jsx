import { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { toast } from 'react-toastify';
import PaystackPop from '@paystack/inline-js';
import {
  Trash2, ChevronDown, CheckCircle, ShoppingCart, Plus, Minus, MapPin,
  Truck, CreditCard, ShoppingBag, X, AlertTriangle,
} from 'lucide-react';
import { OUTSIDE_DUNKWA_TOWNS, containsOutsideTown, getDetectedTown } from '../data/towns';
import ApiUrl from './helper-functions/ApiUrl';
import {
  fetchCartItems,
  selectCarts,
  selectCartsStatus,
  selectCartsError,
  removeCartItem,
  updateCartItem,
  clearCart,
} from '../redux/slice/cartsSlice';
import { createOrder, selectOrdersStatus, selectOrdersError } from '../redux/slice/ordersSlice';
import { selectRiders } from '../redux/slice/ridersSlice';
import Loader from './helper-functions/Loader';
import AllRiders from './AllRiders';
import PaymentCard from './PaymentCard';
import { getSelectedPrice, getSizeLabel, hasMultiplePrices } from '../utils/priceUtils';

const Cart = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const carts = useSelector(selectCarts) || [];
  const status = useSelector(selectCartsStatus);
  const error = useSelector(selectCartsError);
  const orderStatus = useSelector(selectOrdersStatus);
  const orderError = useSelector(selectOrdersError);
  const riders = useSelector(selectRiders);

  // State variables for the order form
  const [selectedRider, setSelectedRider] = useState('');
  const [deliveryAddress, setDeliveryAddress] = useState('');
  const [isOrderingForOthers, setIsOrderingForOthers] = useState(false);
  const [recipientInfo, setRecipientInfo] = useState({
    recipient_name: '',
    recipient_phone: '',
    recipient_address: '',
  });
  const [showOrderForm, setShowOrderForm] = useState(false);

  // Step-by-step wizard state
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;

  // Town selection state for outside Dunkwa deliveries
  const [selectedTown, setSelectedTown] = useState('');

  // Validation error states
  const [addressValidationError, setAddressValidationError] = useState('');
  const [recipientAddressValidationError, setRecipientAddressValidationError] = useState('');
  const [donate, setDonate] = useState(true);
  const [deliveryZone, setDeliveryZone] = useState('');

  const { user } = useSelector((state) => state.auth);
  const customerId = user.id;

  useEffect(() => {
    if (customerId) {
      dispatch(fetchCartItems(customerId));
    }
  }, [dispatch, customerId]);

  // Helper function to get the selected price for a cart item
  const getCartItemPrice = (item) => {
    if (!item.food) return 0;
    const selectedPriceIndex = item.selected_price_index || 0;
    return getSelectedPrice(item.food, selectedPriceIndex);
  };

  const totalCartPrice = carts.reduce((total, item) => {
    const price = getCartItemPrice(item);
    const quantity = item.quantity || 0;
    return total + price * quantity;
  }, 0);

  const selectedRiderDetails = riders.find(
    (rider) => rider.id === selectedRider,
  );

  let deliveryPrice = 0;

  if (selectedRiderDetails) {
    if (deliveryZone === 'within') {
      deliveryPrice = selectedRiderDetails.within_dunkwa_price || 0;
    } else {
      deliveryPrice = selectedRiderDetails.outside_dunkwa_price || 0;
    }
  }

  const totalPrice = totalCartPrice + deliveryPrice + (donate ? 1 : 0);

  const handleRemoveItem = (cartId) => {
    dispatch(removeCartItem({ customerId, cartItemId: cartId }));
  };

  const handleUpdateQuantity = (cartId, newQuantity, currentPriceIndex = 0) => {
    if (newQuantity >= 1) {
      dispatch(updateCartItem({
        customerId,
        cartId,
        quantity: newQuantity,
        priceIndex: currentPriceIndex
      }));
    }
  };

  const handleClearCart = () => {
    dispatch(clearCart(customerId));
  };

  const handleCreateOrder = async () => {
    const foodIds = carts.map((item) => item.food.id);
    const quantities = carts.map((item) => item.quantity);
    const priceIndices = carts.map((item) => item.selected_price_index || 0);

    // Prepare the delivery address with appropriate prefix
    let finalDeliveryAddress = '';
    let finalRecipientAddress = '';

    if (deliveryZone === 'within') {
      finalDeliveryAddress = `Dunkwa, ${deliveryAddress}`;
      finalRecipientAddress = `Dunkwa, ${recipientInfo.recipient_address}`;
    } else if (deliveryZone === 'outside' && selectedTown) {
      finalDeliveryAddress = `${selectedTown}, ${deliveryAddress}`;
      finalRecipientAddress = `${selectedTown}, ${recipientInfo.recipient_address}`;
    } else {
      finalDeliveryAddress = deliveryAddress;
      finalRecipientAddress = recipientInfo.recipient_address;
    }

    const orderData = {
      customerId,
      riderId: selectedRider,
      foodIds,
      quantities,
      priceIndices,
      deliveryAddress: isOrderingForOthers ? finalRecipientAddress : finalDeliveryAddress,
      deliveryPrice,
      donation: donate ? 1 : 0,
    };

    if (isOrderingForOthers) {
      orderData.recipientAttributes = {
        recipient_name: recipientInfo.recipient_name,
        recipient_phone: recipientInfo.recipient_phone,
        recipient_address: finalRecipientAddress,
      };
    }

    try {
      const result = await dispatch(createOrder(orderData)).unwrap();

      // Handle different possible response formats
      const accessCode = result.payment_data?.access_code || result.access_code || result.accessCode;

      // Extract order IDs from the orders array.
      let orderIds = [];
      if (result.orders && Array.isArray(result.orders)) {
        orderIds = result.orders.map(order => order.id);
      } else if (result.order_id || result.orderId || result.id) {
        // Fallback for single order responses
        orderIds = [result.order_id || result.orderId || result.id];
      }

      if (orderIds.length === 0) {
        throw new Error(`Order IDs not received from server. Response: ${JSON.stringify(result)}`);
      }

      if (accessCode && orderIds.length > 0) {
        const popup = new PaystackPop();
        popup.resumeTransaction(accessCode);
        toast.info('Processing payment, please wait...');
      }

      // Use the first order ID for polling (since all orders in a group share the same payment)
      const primaryOrderId = orderIds[0];
    
      if (!primaryOrderId || primaryOrderId === 'undefined' || primaryOrderId === undefined) {
        toast.error('Order created but unable to track status. Please check your orders page.');
      return;
      }

      // Poll the order status.
      const interval = setInterval(async () => {
        try {
          const response = await axios.get(`${ApiUrl}/customers/${customerId}/orders/${primaryOrderId}`);
          const order = response.data;

          if (order.status === 'confirmed') {
            clearInterval(interval);
            toast.success('Payment successful!');
            handleClearCart();
            setDeliveryAddress('');
            setRecipientInfo({ recipient_name: '', recipient_phone: '', recipient_address: '' });
            setSelectedRider('');
            setIsOrderingForOthers(false);
            setShowOrderForm(false);
            navigate('/customer-orders');
          } else if (order.status === 'failed') {
            clearInterval(interval);
            toast.error('Payment failed.');
          }
        } catch (err) {
          // Handle error properly
          clearInterval(interval); // Stop polling on error
          const errorMessage = err?.response?.data?.error || err?.message || 'Error fetching order status';
          toast.error(errorMessage);
        }
      }, 2000);
    } catch (error) {
      // Handle different error formats
      let errorMessage = 'Something went wrong';

      if (error?.errors) {
        // If error has an errors array/object
        errorMessage = typeof error.errors === 'object'
          ? Object.values(error.errors).flat().join(', ')
          : error.errors;
      } else if (error?.message) {
        // If error has a message property
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        // If error is a string
        errorMessage = error;
      }

      toast.error(`Order Error: ${errorMessage}`);
    }
  };





  // Step validation functions (pure validation without side effects)
  const isStep1Valid = () => {
    if (!deliveryZone) return false;

    // For outside Dunkwa deliveries, town selection is required
    if (deliveryZone === 'outside' && !selectedTown) return false;

    if (!isOrderingForOthers) {
      if (deliveryAddress.trim() === '') return false;

      // Check if within Dunkwa address contains outside towns
      if (deliveryZone === 'within' && containsOutsideTown(deliveryAddress)) {
        return false;
      }

      return true;
    } else {
      const hasRequiredFields = recipientInfo.recipient_name.trim() !== '' &&
                               recipientInfo.recipient_phone.trim() !== '' &&
                               recipientInfo.recipient_address.trim() !== '';

      if (!hasRequiredFields) return false;

      // Check if within Dunkwa address contains outside towns
      if (deliveryZone === 'within' && containsOutsideTown(recipientInfo.recipient_address)) {
        return false;
      }

      return true;
    }
  };

  const isStep2Valid = () => {
    return selectedRider !== null && selectedRider !== '';
  };

  const isStep3Valid = () => {
    // Validate that all previous steps are complete and cart is not empty
    return isStep1Valid() && isStep2Valid() && carts.length > 0;
  };

  // Navigation functions
  const goToNextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const closeOrderForm = () => {
    setShowOrderForm(false);
    setCurrentStep(1); // Reset to first step when closing
    setSelectedTown(''); // Reset town selection
    setAddressValidationError(''); // Clear validation errors
    setRecipientAddressValidationError('');
  };

  // Effect to handle validation error messages (separate from validation logic)
  useEffect(() => {
    if (deliveryZone === 'within') {
      // Check delivery address for outside towns
      if (!isOrderingForOthers && deliveryAddress.trim() && containsOutsideTown(deliveryAddress)) {
        const detectedTown = getDetectedTown(deliveryAddress);
        setAddressValidationError(`"${detectedTown}" is outside Dunkwa. Please select "Outside Dunkwa" as your delivery zone.`);
      } else if (!isOrderingForOthers) {
        setAddressValidationError('');
      }

      // Check recipient address for outside towns
      if (isOrderingForOthers && recipientInfo.recipient_address.trim() && containsOutsideTown(recipientInfo.recipient_address)) {
        const detectedTown = getDetectedTown(recipientInfo.recipient_address);
        setRecipientAddressValidationError(`"${detectedTown}" is outside Dunkwa. Please select "Outside Dunkwa" as your delivery zone.`);
      } else if (isOrderingForOthers) {
        setRecipientAddressValidationError('');
      }
    } else {
      // Clear errors when not in 'within' zone
      setAddressValidationError('');
      setRecipientAddressValidationError('');
    }
  }, [deliveryZone, deliveryAddress, recipientInfo.recipient_address, isOrderingForOthers]);



  const handleAddressChange = (value, isRecipient = false) => {
    if (isRecipient) {
      setRecipientInfo(prev => ({ ...prev, recipient_address: value }));
    } else {
      setDeliveryAddress(value);
    }
    // Note: Validation errors are now handled by the useEffect above
  };

  // Get current step validation status
  const getCurrentStepValidation = () => {
    switch (currentStep) {
      case 1: return isStep1Valid();
      case 2: return isStep2Valid();
      case 3: return isStep3Valid();
      default: return false;
    }
  };



  if (status === 'loading') {
    return <Loader />;
  }

  if (status === 'failed') {
    return (
      <div>
        Error:
        {' '}
        {error}
      </div>
    );
  }

  const renderOrderForm = () => (
    <div className="order-form-overlay">
      <div className="order-form-wizard">
        <div className="wizard-header">
          <div className="header-content">
            <h3 className="wizard-title">
              <ShoppingBag className="title-icon" />
              Complete Your Order
            </h3>
            <p className="wizard-subtitle">
              Step {currentStep} of {totalSteps}: {
                currentStep === 1 ? 'Delivery Details' :
                currentStep === 2 ? 'Select Rider' :
                'Payment & Checkout'
              }
            </p>
          </div>
          <button
            className="close-wizard"
            type="button"
            onClick={closeOrderForm}
            aria-label="Close order form"
          >
            <X size={24} />
          </button>
        </div>

        <div className="wizard-progress">
          <div className="progress-bar">
            <div
              className="progress-fill"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            ></div>
          </div>
          <div className="progress-steps">
            {[1, 2, 3].map((step) => (
              <div
                key={step}
                className={`progress-step ${
                  step < currentStep ? 'completed' :
                  step === currentStep ? 'active' :
                  'pending'
                }`}
              >
                <div className="step-number">
                  {step < currentStep ? <CheckCircle size={16} /> : step}
                </div>
                <span className="step-label">
                  {step === 1 ? 'Delivery' : step === 2 ? 'Rider' : 'Payment'}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="wizard-content">
          {/* Step 1: Delivery Details */}
          {currentStep === 1 && (
            <div className="step-content delivery-step">
              <div className="step-header">
                <MapPin className="step-icon" size={24} />
                <div>
                  <h4 className="step-title">Delivery Details</h4>
                  <p className="step-description">Where should we deliver your delicious food?</p>
                </div>
              </div>

              <div className="step-form">
                <div className="form-group">
                  <label className="form-label">Who is this order for?</label>
                  <div className="radio-group">
                    <label className="radio-option">
                      <input
                        type="radio"
                        name="orderFor"
                        value="myself"
                        checked={!isOrderingForOthers}
                        onChange={() => setIsOrderingForOthers(false)}
                      />
                      <span className="radio-label">Myself</span>
                    </label>
                    <label className="radio-option">
                      <input
                        type="radio"
                        name="orderFor"
                        value="someone"
                        checked={isOrderingForOthers}
                        onChange={() => setIsOrderingForOthers(true)}
                      />
                      <span className="radio-label">Someone Else</span>
                    </label>
                  </div>
                </div>

                <div className="form-group">
                  <label className="form-label">Delivery Zone</label>
                  <select
                    className="form-select"
                    value={deliveryZone}
                    onChange={(e) => {
                      const newZone = e.target.value;
                      setDeliveryZone(newZone);
                      setDeliveryAddress('');
                      setSelectedTown(''); // Reset town selection
                      setAddressValidationError(''); // Clear validation errors
                      setRecipientAddressValidationError('');
                      setRecipientInfo({
                        ...recipientInfo,
                        recipient_address: '',
                      });
                    }}
                    required
                  >
                    <option value="">Select delivery zone</option>
                    <option value="within">Within Dunkwa</option>
                    <option value="outside">Outside Dunkwa</option>
                  </select>
                </div>

                {/* Town selection for outside Dunkwa deliveries */}
                {deliveryZone === 'outside' && (
                  <div className="form-group">
                    <label className="form-label">Select Town</label>
                    <select
                      className="form-select"
                      value={selectedTown}
                      onChange={(e) => setSelectedTown(e.target.value)}
                      required
                    >
                      <option value="">Select your town</option>
                      {OUTSIDE_DUNKWA_TOWNS.map((town) => (
                        <option key={town} value={town}>
                          {town}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                {!isOrderingForOthers ? (
                  <div className="form-group">
                    <label className="form-label">Your Address</label>
                    <div className="address-input-group">
                      {deliveryZone === 'within' ? (
                        <div className="prefixed-input">
                          <span className="input-prefix">Dunkwa, </span>
                          <input
                            type="text"
                            className="form-input"
                            placeholder="Street name, house number, etc."
                            value={deliveryAddress}
                            onChange={(e) => handleAddressChange(e.target.value)}
                          />
                        </div>
                      ) : deliveryZone === 'outside' ? (
                        <div className="prefixed-input">
                          <span className="input-prefix">{selectedTown ? `${selectedTown}, ` : ''}</span>
                          <input
                            type="text"
                            className="form-input"
                            placeholder={selectedTown ? "Street name, house number, etc." : "Select town first"}
                            value={deliveryAddress}
                            onChange={(e) => setDeliveryAddress(e.target.value)}
                            disabled={!selectedTown}
                          />
                        </div>
                      ) : (
                        <input
                          type="text"
                          className="form-input"
                          placeholder="Select delivery zone first"
                          value={deliveryAddress}
                          onChange={(e) => setDeliveryAddress(e.target.value)}
                          disabled
                        />
                      )}
                    </div>
                    {/* Address validation error */}
                    {addressValidationError && (
                      <div className="validation-error">
                        <AlertTriangle size={16} />
                        <span>{addressValidationError}</span>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="recipient-details">
                    <div className="form-group">
                      <label className="form-label">Recipient Name</label>
                      <input
                        type="text"
                        className="form-input"
                        placeholder="Enter recipient's full name"
                        value={recipientInfo.recipient_name}
                        onChange={(e) => setRecipientInfo({
                          ...recipientInfo, recipient_name: e.target.value,
                        })}
                      />
                    </div>
                    <div className="form-group">
                      <label className="form-label">Recipient Phone</label>
                      <input
                        type="tel"
                        className="form-input"
                        placeholder="Enter recipient's phone number"
                        value={recipientInfo.recipient_phone}
                        onChange={(e) => setRecipientInfo({
                          ...recipientInfo, recipient_phone: e.target.value,
                        })}
                      />
                    </div>
                    <div className="form-group">
                      <label className="form-label">Recipient Address</label>
                      <div className="address-input-group">
                        {deliveryZone === 'within' ? (
                          <div className="prefixed-input">
                            <span className="input-prefix">Dunkwa, </span>
                            <input
                              type="text"
                              className="form-input"
                              placeholder="Street name, house number, etc."
                              value={recipientInfo.recipient_address}
                              onChange={(e) => handleAddressChange(e.target.value, true)}
                            />
                          </div>
                        ) : deliveryZone === 'outside' ? (
                          <div className="prefixed-input">
                            <span className="input-prefix">{selectedTown ? `${selectedTown}, ` : ''}</span>
                            <input
                              type="text"
                              className="form-input"
                              placeholder={selectedTown ? "Street name, house number, etc." : "Select town first"}
                              value={recipientInfo.recipient_address}
                              onChange={(e) => setRecipientInfo({
                                ...recipientInfo, recipient_address: e.target.value,
                              })}
                              disabled={!selectedTown}
                            />
                          </div>
                        ) : (
                          <input
                            type="text"
                            className="form-input"
                            placeholder="Select delivery zone first"
                            value={recipientInfo.recipient_address}
                            onChange={(e) => setRecipientInfo({
                              ...recipientInfo, recipient_address: e.target.value,
                            })}
                            disabled
                          />
                        )}
                      </div>
                      {/* Recipient address validation error */}
                      {recipientAddressValidationError && (
                        <div className="validation-error">
                          <AlertTriangle size={16} />
                          <span>{recipientAddressValidationError}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Step 2: Rider Selection */}
          {currentStep === 2 && (
            <div className="step-content rider-step">
              <div className="step-header">
                <Truck className="step-icon" size={24} />
                <div>
                  <h4 className="step-title">Select Your Rider</h4>
                  <p className="step-description">Choose your preferred delivery rider</p>
                </div>
              </div>

              <div className="step-form">
                <AllRiders
                  selectedRider={selectedRider}
                  onRiderChange={(value) => setSelectedRider(Number(value))}
                  deliveryZone={deliveryZone}
                />
              </div>
            </div>
          )}

          {/* Step 3: Payment */}
          {currentStep === 3 && (
            <div className="step-content payment-step">
              <div className="step-header">
                <CreditCard className="step-icon" size={24} />
                <div>
                  <h4 className="step-title">Payment & Checkout</h4>
                  <p className="step-description">Review your order and complete payment</p>
                </div>
              </div>

              <div className="step-form">
                <PaymentCard
                  totalPrice={totalPrice}
                  foodPrice={totalCartPrice}
                  deliveryFee={deliveryPrice}
                  donate={donate}
                  setDonate={setDonate}
                  triggerPayment={handleCreateOrder}
                  disabled={orderStatus === 'loading' || !getCurrentStepValidation()}
                />
              </div>
            </div>
          )}
        </div>

        {/* Wizard Navigation */}
        <div className="wizard-navigation">
          <div className="nav-buttons">
            {currentStep > 1 && (
              <button
                type="button"
                className="nav-button back-button"
                onClick={goToPreviousStep}
              >
                <ChevronDown className="button-icon rotated" size={16} />
                Back
              </button>
            )}

            <div className="nav-spacer"></div>

            {currentStep < totalSteps && (
              <button
                type="button"
                className={`nav-button next-button ${!getCurrentStepValidation() ? 'disabled' : ''}`}
                onClick={goToNextStep}
                disabled={!getCurrentStepValidation()}
              >
                Next
                <ChevronDown className="button-icon" size={16} />
              </button>
            )}
          </div>
        </div>

        {orderError && (
          <div className="wizard-error">
            <div className="error-content">
              <AlertTriangle size={20} />
              <span>
                {typeof orderError === 'object'
                  ? (orderError.error || JSON.stringify(orderError))
                  : orderError}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="cart-container">
      <div className="cart-header">
        <ShoppingCart className="cart-header-icon" />
        <h2 className="cart-title">My Cart</h2>
      </div>

      {carts.length > 0 ? (
        <div className="cart-inner">
          <ul className="cart-items">
            {carts.map((item) => (
              <li key={item.id} className="cart-item">
                <div className="cart-item-header">
                  <div className="cart-item-image-container">
                    <img
                      className="cart-food-image"
                      src={item.food ? item.food.food_image_url : 'Loading...'}
                      alt={item.food ? item.food.name : 'Loading...'}
                    />
                  </div>
                  <div className="cart-item-details">
                    <h4 className="cart-item-name">
                      {item.food ? item.food.name : 'Loading...'}
                    </h4>
                    <p className="cart-item-description">
                      {item.food && item.food.description ? item.food.description : 'No description available'}
                    </p>
                    <div className="price-display">
                      <span className="price-label">Price:</span>
                      <span className="price-value">
                        ₵{getCartItemPrice(item).toFixed(2)}
                      </span>
                    </div>

                    {/* Show price option selection for multiple prices */}
                    {item.food && hasMultiplePrices(item.food) && (
                      <div className="price-option-display">
                        <span className="price-option-label">
                          Size/Option: <strong>{getSizeLabel(item.selected_price_index || 0)}</strong>
                        </span>
                        <select
                          className="price-option-select"
                          value={item.selected_price_index || 0}
                          onChange={(e) => {
                            const newPriceIndex = parseInt(e.target.value);

                            // Update cart item with new price index
                            dispatch(updateCartItem({
                              customerId,
                              cartId: item.id,
                              quantity: item.quantity,
                              priceIndex: newPriceIndex
                            }))
                            .then(() => {
                              toast.success('Price option updated!');
                            })
                            .catch(() => {
                              toast.error('Failed to update price option.');
                            });
                          }}
                        >
                          {item.food.prices.map((price, index) => (
                            <option key={index} value={index}>
                              {getSizeLabel(index)} - ₵{price.toFixed(2)}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}
                  </div>
                  <button
                    className="remove-item"
                    aria-label="Remove Item"
                    type="button"
                    onClick={() => handleRemoveItem(item.id)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        handleRemoveItem(item.id);
                      }
                    }}
                  >
                    <Trash2 size={18} />
                  </button>
                </div>

                <div className="cart-item-footer">
                  <div className="cart-controls">
                    <button
                      type="button"
                      className="quantity-button"
                      aria-label="Decrease quantity"
                      onClick={() => handleUpdateQuantity(item.id, item.quantity - 1, item.selected_price_index || 0)}
                      disabled={item.quantity <= 1}
                    >
                      <Minus size={16} />
                    </button>
                    <span className="quantity-value">{item.quantity}</span>
                    <button
                      type="button"
                      className="quantity-button"
                      aria-label="Increase quantity"
                      onClick={() => handleUpdateQuantity(item.id, item.quantity + 1, item.selected_price_index || 0)}
                    >
                      <Plus size={16} />
                    </button>
                  </div>
                  <div className="item-subtotal">
                    <span className="subtotal-label">Subtotal:</span>
                    <span className="subtotal-value">
                      ₵{(getCartItemPrice(item) * item.quantity).toFixed(2)}
                    </span>
                  </div>
                </div>
              </li>
            ))}
          </ul>

          <div className="cart-summary">
            <div className="total-price">
              <span className="total-label">Total:</span>
              <span className="total-value">
                ₵
                {totalCartPrice.toFixed(2)}
              </span>
            </div>
            <div className="cart-actions">
              <button
                className="action-button clear-button"
                type="button"
                onClick={handleClearCart}
              >
                Clear Cart
              </button>
              <button
                className="action-button order-button"
                type="button"
                onClick={() => setShowOrderForm(true)}
              >
                Proceed to Checkout
              </button>
            </div>
          </div>

          {showOrderForm && renderOrderForm()}
        </div>
      ) : (
        <div className="empty-cart">
          <ShoppingCart size={64} className="empty-cart-icon" />
          <p>Your cart is empty</p>
          <p className="empty-cart-subtext">Add some delicious items to get started!</p>
        </div>
      )}
    </div>
  );
};

export default Cart;
