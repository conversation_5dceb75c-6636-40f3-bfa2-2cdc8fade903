import { useEffect } from 'react';
import PropTypes from 'prop-types';
import { FaTimes } from 'react-icons/fa';

const DocumentModal = ({ imageUrl, title, onClose }) => {
  // Close modal when Escape key is pressed
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscKey);

    // Prevent body scrolling when modal is open
    document.body.style.overflow = 'hidden';

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'auto';
    };
  }, [onClose]);

  // Close modal when clicking outside the image
  const handleBackdropClick = (e) => {
    if (e.target.classList.contains('document-modal-overlay')) {
      onClose();
    }
  };

  // Handle keyboard events for backdrop
  const handleBackdropKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      if (e.target.classList.contains('document-modal-overlay')) {
        onClose();
      }
    }
  };

  return (
    <div
      className="document-modal-overlay"
      onClick={handleBackdropClick}
      onKeyDown={handleBackdropKeyDown}
      tabIndex={0}
      role="button"
      aria-labelledby="modal-title"
      aria-label="Close modal by clicking backdrop"
    >
      <div className="document-modal-content">
        <div className="document-modal-header">
          <h3>{title}</h3>
          <button
            type="button"
            className="document-modal-close"
            onClick={onClose}
            aria-label="Close document viewer"
          >
            <FaTimes />
          </button>
        </div>
        <div className="document-modal-body">
          <img src={imageUrl} alt={title} />
        </div>
      </div>
    </div>
  );
};

DocumentModal.propTypes = {
  title: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired,
  imageUrl: PropTypes.string.isRequired,
};

export default DocumentModal;
