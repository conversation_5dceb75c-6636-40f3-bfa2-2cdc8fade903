/* Admin Dashboard - Modern Food Delivery System */
:root {
  --primary-color: #f8b400;
  --primary-dark: #e5a700;
  --primary-light: rgba(248, 180, 0, 0.1);
  --primary-lighter: rgba(248, 180, 0, 0.05);
  --secondary-color: #4caf50;
  --secondary-dark: #388e3c;
  --danger-color: #f44336;
  --danger-dark: #d32f2f;
  --danger-light: rgba(244, 67, 54, 0.1);
  --info-color: #2196f3;
  --info-dark: #0b7dda;
  --info-light: rgba(33, 150, 243, 0.1);
  --warning-color: #ff9800;
  --warning-dark: #f57c00;
  --warning-light: rgba(255, 152, 0, 0.1);
  --success-color: #4caf50;
  --success-dark: #388e3c;
  --success-light: rgba(76, 175, 80, 0.1);
  --text-primary: #333;
  --text-secondary: #666;
  --text-light: #999;
  --bg-light: #f8f9fa;
  --bg-white: #fff;
  --border-color: #e0e0e0;
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow-md: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --transition: all 0.3s ease;

  /* Status colors */
  --status-pending: #ff9800;
  --status-processing: #2196f3;
  --status-confirmed: #2196f3;
  --status-ready: #9c27b0;
  --status-picked-up: #ff5722;
  --status-delivered: #4caf50;
  --status-received: #009688;
  --status-cancelled: #f44336;
  --status-rejected: #757575;
}

/* Main Layout */
.admin-dashboard {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-light);
  color: var(--text-primary);
  font-family: 'Poppins', 'Open Sans', sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Overlay for when sidebar is open */
.admin-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
  transition: opacity 0.3s ease;
}

.admin-overlay.active {
  display: block;
}

/* Dashboard overlay for mobile */
.dashboard-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
  transition: opacity 0.3s ease;
}

.dashboard-overlay.active {
  display: block;
}

.admin-sidebar {
  width: 280px;
  background-color: var(--bg-white);
  box-shadow: var(--shadow-md);
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  z-index: 1010;
  transition: transform 0.3s ease, width 0.3s ease;
  left: 0;
  top: 0;
}

.admin-table th {
  background-color: rgba(248, 180, 0, 0.05);
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
}

.admin-table th,
.admin-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  white-space: nowrap;
}

.monthly-signups-section .admin-table th,
.monthly-signups-section .admin-table td,
.top-customers-section .admin-table th,
.top-customers-section .admin-table td {
  white-space: normal;
  padding: 0.75rem;
}

.admin-nav-badge {
  position: absolute;
  top: 0.5rem;
  right: 1rem;
  background-color: var(--danger-color);
  color: white;
  border-radius: 50%;
  min-width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
  padding: 0 0.25rem;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
  animation: pulse-badge 2s infinite;
}

.admin-sidebar.collapsed {
  width: 70px;
}

.admin-sidebar.collapsed .admin-logo span,
.admin-sidebar.collapsed .admin-nav-item span {
  display: none;
}

.admin-sidebar.collapsed .admin-nav-item {
  justify-content: center;
  padding: 0.85rem 0;
  width: 100%;
  margin: 0.4rem 0;
}

.admin-sidebar.collapsed .admin-nav-icon {
  margin-right: 0;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-sidebar.collapsed .admin-nav-badge {
  right: 0.5rem;
  top: 0.25rem;
}

.admin-sidebar-header {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-color);
}

.admin-sidebar-toggle.desktop-toggle {
  display: none;
  background: var(--primary-color);
  border: none;
  border-radius: 20%;
  width: 2.5rem;
  height: 2.5rem;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0.125rem 0.25rem rgba(248, 180, 0, 0.3);
  cursor: pointer;
  color: #fff;
  transition: all 0.3s ease;
}

.admin-sidebar-toggle.desktop-toggle:hover {
  transform: scale(1.05);
  box-shadow: 0 0.25rem 0.5rem rgba(248, 180, 0, 0.4);
}

.admin-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--primary-color);
  font-weight: 700;
  font-size: 1.25rem;
}

.admin-logo-icon {
  font-size: 1.5rem;
}

.admin-sidebar-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 1.25rem;
  transition: var(--transition);
}

.admin-sidebar-toggle:hover {
  color: var(--primary-color);
}

.admin-nav {
  padding: 1rem 0;
}

.admin-nav-item {
  width: 95%;
  display: flex;
  align-items: center;
  padding: 0.85rem 1.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  margin: 0.4rem;
  border-radius: 0.5rem;
  border: none;
  background: none;
}

.admin-nav-item:hover {
  background-color: rgba(248, 180, 0, 0.1);
  color: var(--primary-color);
  transform: translateX(5px);
}

.admin-nav-item.active {
  background-color: rgba(248, 180, 0, 0.15);
  color: var(--primary-color);
  font-weight: 600;
  border-left: 3px solid var(--primary-color);
  box-shadow: 0 2px 5px rgba(248, 180, 0, 0.2);
}

.admin-nav-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
  width: 1.5rem;
  text-align: center;
  color: inherit;
  transition: transform 0.3s ease;
  min-width: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-nav-item:hover .admin-nav-icon {
  transform: scale(1.1);
}

.admin-nav-item:hover .admin-nav-badge {
  transform: scale(1.1);
  background-color: #ff1a1a;
}

@keyframes pulse-badge {
  0% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.7);
  }

  70% {
    box-shadow: 0 0 0 6px rgba(244, 67, 54, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0);
  }
}

.admin-main {
  flex: 1;
  margin-left: 280px;
  padding: 6rem 0.5rem 6rem 0.5rem;
  transition: all 0.3s ease;
  width: calc(100% - 280px);
}

.admin-main.expanded {
  margin-left: 70px;
  width: calc(100% - 70px);
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.admin-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.admin-user {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.admin-user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
}

.admin-user-info {
  display: flex;
  flex-direction: column;
}

.admin-user-name {
  font-weight: 600;
  color: var(--text-primary);
}

.admin-user-role {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.error-message {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--danger-color);
  padding: 1rem;
  border-radius: var(--border-radius-sm);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-left: 4px solid var(--danger-color);
}

.error-icon {
  font-size: 1.5rem;
}

.admin-table-container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  margin-bottom: 1.5rem;
  background-color: var(--bg-white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 0;
  background-color: var(--bg-white);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.admin-table tr:last-child td {
  border-bottom: none;
}

.admin-table tr:hover td {
  background-color: rgba(248, 180, 0, 0.02);
}

.status-unknown {
  background-color: rgba(158, 158, 158, 0.1);
  color: var(--text-secondary);
}

.service-fees-tab {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  max-width: 600px;
  margin: 0 auto;
}

.service-fee-form {
  display: grid;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: var(--text-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-group input {
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 1rem;
  transition: var(--transition);
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(248, 180, 0, 0.1);
}

.fee-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: rgba(33, 150, 243, 0.1);
  color: var(--info-color);
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius-sm);
  margin-bottom: 1rem;
}

.update-fees-button {
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.update-fees-button:hover {
  background-color: var(--primary-dark);
}

.update-fees-button:disabled {
  background-color: var(--text-light);
  cursor: not-allowed;
}

.view-button {
  padding: 0.625rem 1.25rem;
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-right: 0.5rem;
  background-color: var(--info-color);
  color: white;
}

.approve-button {
  background-color: var(--success-color);
  color: white;
}

.view-button:hover {
  background-color: var(--info-dark);
}

/* Dashboard Overview */
.dashboard-tab {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dashboard-section-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dashboard-section-icon {
  color: var(--primary-color);
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Monthly details styling */
.monthly-details {
  margin-top: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  max-height: 150px;
  overflow-y: auto;
  background-color: var(--bg-light);
  border-radius: var(--border-radius-sm);
  padding: 0.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.monthly-item {
  display: flex;
  justify-content: space-between;
  padding: 0.35rem 0.5rem;
  border-bottom: 1px dashed var(--border-color);
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.monthly-item:hover {
  background-color: rgba(248, 180, 0, 0.05);
}

.monthly-item:last-child {
  border-bottom: none;
}

.monthly-item .month {
  font-weight: 500;
  color: var(--text-primary);
}

.monthly-item .value {
  font-weight: 600;
  color: var(--primary-color);
  background-color: rgba(248, 180, 0, 0.1);
  padding: 0.1rem 0.5rem;
  border-radius: 1rem;
}

.stat-card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  border-left: 4px solid var(--primary-color);
  display: flex;
  align-items: center;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background-color: rgba(248, 180, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: var(--primary-color);
  font-size: 1.5rem;
}

.stat-content {
  flex: 1;
}

.stat-card h3 {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.stat-card.orders {
  border-left-color: var(--primary-color);
}

.stat-card.orders .stat-icon {
  background-color: rgba(248, 180, 0, 0.1);
  color: var(--primary-color);
}

.stat-card.customers {
  border-left-color: var(--info-color);
}

.stat-card.customers .stat-icon {
  background-color: rgba(33, 150, 243, 0.1);
  color: var(--info-color);
}

.stat-card.vendors {
  border-left-color: var(--success-color);
}

.stat-card.vendors .stat-icon {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

.stat-card.riders {
  border-left-color: var(--warning-color);
}

.stat-card.riders .stat-icon {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--warning-color);
}

.stat-card.revenue {
  border-left-color: var(--secondary-color);
}

.stat-card.revenue .stat-icon {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--secondary-color);
}

.stat-card.fees {
  border-left-color: var(--danger-color);
}

.stat-card.fees .stat-icon {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--danger-color);
}

/* Complaint and Suggestion Stat Cards */
.stat-card.complaints {
  border-left-color: var(--danger-color);
}

.stat-card.complaints .stat-icon {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--danger-color);
}

.stat-card.suggestions {
  border-left-color: var(--info-color);
}

.stat-card.suggestions .stat-icon {
  background-color: rgba(33, 150, 243, 0.1);
  color: var(--info-color);
}

.stat-card.pending {
  border-left-color: var(--warning-color);
}

.stat-card.pending .stat-icon {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--warning-color);
}

.stat-card.in-progress {
  border-left-color: var(--info-color);
}

.stat-card.in-progress .stat-icon {
  background-color: rgba(33, 150, 243, 0.1);
  color: var(--info-color);
}

.stat-card.resolved {
  border-left-color: var(--success-color);
}

.stat-card.resolved .stat-icon {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

.stat-card.implemented {
  border-left-color: var(--success-color);
}

.stat-card.implemented .stat-icon {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

.stat-card.rejected {
  border-left-color: var(--status-rejected);
}

.stat-card.rejected .stat-icon {
  background-color: rgba(117, 117, 117, 0.1);
  color: var(--status-rejected);
}

/* Dashboard Alerts */
.dashboard-alerts {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  margin-bottom: 2rem;
}

.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
}

.alert-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.alert-item:first-child {
  padding-top: 0;
}

.alert-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.alert-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: rgba(244, 67, 54, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--danger-color);
  font-size: 1.25rem;
}

.alert-text {
  font-weight: 500;
}

.alert-text strong {
  color: var(--danger-color);
}

/* Dashboard Statistics */
.dashboard-statistics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.performance-statistics {
  grid-template-columns: 1fr;
}

.statistics-section {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-md);
  padding: 0.5rem;
  margin-bottom: 1rem;
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.complaints-suggestions-statistics {
  grid-column: span 2;
}

.complaints-suggestions-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 992px) {
  .complaints-suggestions-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

.suggestions-section,
.complaints-section {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-md);
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.statistics-subsection {
  margin-bottom: 1.5rem;
}

.statistics-subsection-title {
  font-size: 0.95rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
  color: var(--text-secondary);
}

.category-stats-grid,
.status-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 0.75rem;
}

.category-stat-item,
.status-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  border-radius: var(--border-radius-sm);
  background-color: var(--bg-light);
  text-align: center;
  min-height: 100px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.category-name,
.status-name {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
}

.category-count,
.status-count {
  font-size: 2rem;
  font-weight: 700;
}

.status-stat-item.implemented {
  background-color: rgba(76, 175, 80, 0.15);
  color: var(--success-color);
}

.status-stat-item.pending {
  background-color: rgba(255, 152, 0, 0.15);
  color: var(--warning-color);
}

.status-stat-item.under-review {
  background-color: rgba(33, 150, 243, 0.15);
  color: var(--info-color);
}

.status-stat-item.rejected {
  background-color: rgba(244, 67, 54, 0.15);
  color: var(--danger-color);
}

.status-stat-item.resolved {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

/* Orders Statistics Styles */
.orders-statistics {
  grid-column: span 2;
}

.orders-status-container {
  padding: 1rem;
}

.orders-status-container .status-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
}

.status-stat-item.canceled {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--danger-color);
}

.status-stat-item.confirmed {
  background-color: rgba(33, 150, 243, 0.1);
  color: var(--info-color);
}

.status-stat-item.delivered {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

.status-stat-item.out-for-delivery {
  background-color: rgba(156, 39, 176, 0.1);
  color: #9c27b0;
}

.status-stat-item.processing {
  background-color: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.status-stat-item.ready-for-pickup {
  background-color: rgba(0, 188, 212, 0.1);
  color: #00bcd4;
}

.status-stat-item.received {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

.status-stat-item.active {
  background-color: rgba(76, 175, 80, 0.15);
  color: var(--success-color);
}

.statistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.statistics-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.statistics-icon {
  color: var(--primary-color);
}

.statistics-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.statistics-content .stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

@media (min-width: 992px) {
  .statistics-content .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.statistics-month-container {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.statistics-month-title {
  background-color: rgba(248, 180, 0, 0.1);
  color: var(--primary-color);
  padding: 0.75rem 1rem;
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
}

/* Vendor Sales and Rider Deliveries Statistics */
.vendor-sales-statistics,
.rider-deliveries-statistics {
  margin-bottom: 1rem;
}

.vendor-sales-statistics .statistics-month-title {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

.rider-deliveries-statistics .statistics-month-title {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--warning-color);
}

.no-data-message {
  padding: 1rem;
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
}

/* Customer Statistics Styles */
.customer-statistics-container {
  grid-template-columns: 1fr;
}

.customer-statistics {
  width: 100%;
}

.customer-statistics-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Rider Statistics Styles */
.vendor-statistics,
.rider-statistics {
  grid-column: span 2;
  margin-top: 1.5rem;
}

.vendor-stats-grid,
.rider-stats-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.vendor-stat-card,
.rider-stat-card {
  display: flex;
  align-items: center;
  padding: 1.25rem;
  background-color: var(--bg-white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.vendor-stat-icon,
.rider-stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: rgba(var(--primary-rgb), 0.1);
  border-radius: 50%;
  margin-right: 1rem;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.vendor-stat-info,
.rider-stat-info {
  flex: 1;
}

.vendor-stat-info h4,
.rider-stat-info h4 {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin-bottom: 0.25rem;
}

.vendor-stat-value,
.rider-stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-dark);
}

.vendor-status-section,
.rider-status-section {
  padding: 1.25rem;
  background-color: var(--bg-white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.top-vendors-revenue-section,
.top-riders-revenue-section {
  padding: 1.25rem;
  background-color: var(--bg-white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.no-data-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.empty-table-placeholder {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  overflow: hidden;
}

.empty-data-cell {
  text-align: center;
  padding: 1.5rem;
  color: var(--text-muted);
  font-style: italic;
}

@media (max-width: 768px) {
  .vendor-stats-grid,
  .rider-stats-grid {
    grid-template-columns: 1fr;
  }
}

.customer-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

@media (min-width: 992px) {
  .customer-stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.customer-stat-card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-md);
  padding: 0.5rem;
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: 1rem;
  border: 1px solid var(--border-color);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.customer-stat-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.customer-stat-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background-color: rgba(248, 180, 0, 0.1);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.customer-stat-info {
  flex: 1;
}

.customer-stat-info h4 {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.customer-stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.customer-statistics-sections {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

@media (min-width: 992px) {
  .customer-statistics-sections {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 991px) {
  .customer-statistics-sections {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .customer-stats-grid {
    grid-template-columns: 1fr;
  }

  .monthly-signups-section .admin-table,
  .top-customers-section .admin-table {
    font-size: 0.875rem;
  }

  .monthly-signups-section .admin-table th,
  .monthly-signups-section .admin-table td,
  .top-customers-section .admin-table th,
  .top-customers-section .admin-table td {
    padding: 0.5rem;
  }
}

.monthly-signups-section,
.top-customers-section {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: 1rem;
  border: 1px solid var(--border-color);
  width: 100%;
}

/* Override table styles for customer statistics */
.monthly-signups-section .admin-table-container,
.top-customers-section .admin-table-container {
  overflow-x: visible;
  box-shadow: none;
}

.monthly-signups-section .admin-table,
.top-customers-section .admin-table {
  min-width: auto;
  width: 100%;
}

.price-cell {
  font-weight: 500;
  display: flex;
  align-items: center;
}

/* Improve price cell display in customer statistics */
.top-customers-section .price-cell {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.top-customers-section .currency {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.top-customers-section .amount {
  font-weight: 600;
  color: var(--text-primary);
}

.statistics-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem 0;
  padding-left: 0.75rem;
  border-left: 3px solid var(--primary-color);
}

/* Dashboard Recent */
.dashboard-recent {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.recent-orders,
.recent-payouts,
.recent-complaints,
.recent-suggestions {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.recent-orders .admin-table,
.recent-payouts .admin-table,
.recent-complaints .admin-table,
.recent-suggestions .admin-table {
  min-width: 650px;
}

.recent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.recent-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.recent-icon {
  color: var(--primary-color);
}

.view-all {
  color: var(--primary-color);
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  transition: var(--transition);
}

.view-all:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Pending Vendors and Riders */
.pending-vendors-list,
.pending-riders-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  padding-bottom: 6rem;
}

.pending-vendor-card,
.pending-rider-card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
}

.pending-vendor-card:hover,
.pending-rider-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.pending-card-header {
  padding: 1.25rem 1.5rem;
  background-color: rgba(248, 180, 0, 0.1);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pending-card-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pending-card-header h3::before {
  content: '';
  display: inline-block;
  width: 0.5rem;
  height: 0.5rem;
  background-color: var(--warning-color);
  border-radius: 50%;
}

.pending-card-body {
  padding: 0.5rem;
  background-color: var(--bg-white);
}

.vendor-details,
.rider-details {
  margin-bottom: 1.5rem;
  background-color: var(--bg-light);
  border-radius: var(--border-radius-sm);
  padding: 0.3rem;
  border: 1px solid var(--border-color);
}

.rider-details {
  flex-direction: column;
}

.detail-item {
  display: flex;
  padding-bottom: 0.75rem;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.05);
}

.detail-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
  margin-right: 0;
}

.detail-label {
  font-weight: 500;
  color: var(--text-secondary);
  width: 120px;
  flex-shrink: 0;
  position: relative;
}

.detail-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0.25rem;
  height: 0.25rem;
  background-color: var(--primary-color);
  border-radius: 50%;
}

.detail-value {
  color: var(--text-primary);
  font-weight: 500;
}

.document-section {
  margin-top: 1.5rem;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-light);
  border-radius: var(--border-radius-sm);
  padding: 1.5rem;
  border: 1px solid var(--border-color);
}

.document-section h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.document-section h4::before {
  content: '\f15b';
  font-weight: 900;
  color: var(--primary-color);
  font-size: 1rem;
}

.document-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.document-item {
  background-color: var(--bg-light);
  border-radius: var(--border-radius-sm);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.document-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.document-item p {
  padding: 0.5rem;
  margin: 0;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  background-color: rgba(248, 180, 0, 0.05);
  color: var(--text-secondary);
}

.document-image-container {
  position: relative;
  overflow: hidden;
}

.document-item img {
  width: 100%;
  height: 120px;
  object-fit: cover;
  display: block;
  transition: var(--transition);
}

.document-expand-button {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  color: var(--primary-color);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transform: translateY(0.5rem);
  transition: var(--transition);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.document-expand-button:hover {
  background-color: white;
  color: var(--primary-dark);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.document-image-container:hover .document-expand-button {
  opacity: 1;
  transform: translateY(0);
}

.vendor-actions,
.rider-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

/* Table Header Actions */
.table-header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.search-input {
  padding: 0.75rem 1rem;
  padding-left: 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.875rem;
  width: 300px;
  transition: var(--transition);
  background-color: var(--bg-white);
}

.table-search::before {
  content: '\f002';
  font-weight: 900;
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.delete-button {
  color: var(--danger-color);
  background-color: rgba(220, 53, 69, 0.1);
}

/* Responsive */
@media (max-width: 1200px) {
  .dashboard-statistics,
  .dashboard-recent {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) {
  .admin-sidebar.collapsed {
    width: 130px;
    transform: translateX(0);
  }

  .admin-sidebar.collapsed .admin-logo span,
  .admin-sidebar.collapsed .admin-nav-item span {
    display: none;
  }

  .admin-sidebar.collapsed .admin-nav-item {
    justify-content: center;
    padding: 0.85rem 0;
    width: 100%;
    margin: 0.4rem 0;
  }

  .admin-sidebar.collapsed .admin-nav-icon {
    margin-right: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .admin-sidebar.collapsed .admin-nav-badge {
    right: 0.5rem;
    top: 0.25rem;
  }

  .admin-main.expanded {
    margin-left: 140px;
    width: calc(100% - 70px);
  }

  /* Show desktop toggle button. */
  .admin-sidebar-toggle.desktop-toggle {
    display: flex;
  }

  /* Hide mobile toggle button on desktop. */
  .admin-sidebar-toggle:not(.desktop-toggle) {
    display: none;
  }
}

@media (max-width: 1024px) {
  .admin-sidebar {
    width: 220px;
  }

  .admin-main {
    margin-left: 220px;
    width: calc(100% - 220px);
  }
}

@media (max-width: 768px) {
  /* Mobile sidebar behavior */
  .admin-sidebar {
    transform: translateX(-100%);
    width: 280px;
  }

  .admin-sidebar.collapsed {
    transform: translateX(-100%);
    width: 280px;
  }

  .admin-sidebar:not(.collapsed) {
    transform: translateX(0);
  }

  /* Mobile toggle button */
  .admin-sidebar-toggle {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    background-color: var(--primary-color);
    color: white;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 20%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0.125rem 0.25rem rgba(248, 180, 0, 0.3);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .admin-sidebar:not(.collapsed) .admin-sidebar-toggle {
    left: 230px;
    background-color: var(--danger-color);
  }

  .admin-sidebar-toggle:hover {
    transform: scale(1.05);
    box-shadow: 0 0.25rem 0.5rem rgba(248, 180, 0, 0.4);
  }

  /* Mobile nav styling */
  .admin-nav {
    display: flex;
    flex-direction: column;
    padding: 1rem 0;
  }

  .admin-nav-item {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-size: 1rem;
    position: relative;
  }

  .admin-nav-badge {
    top: 50%;
    transform: translateY(-50%);
    right: 1.5rem;
  }

  .admin-nav-icon {
    font-size: 1.25rem;
    width: 1.5rem;
  }

  /* Main content area */
  .admin-main {
    margin-left: 0;
    width: 100%;
  }

  .admin-main.expanded {
    margin-left: 0;
    width: 100%;
  }

  .dashboard-stats {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .admin-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  /* Table scrolling improvements */
  .admin-table-container {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin-bottom: 1.5rem;
    background-color: var(--bg-white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    position: relative;
  }

  .admin-table {
    min-width: 650px;
    width: 100%;
    margin-bottom: 0;
    box-shadow: none;
    border-radius: 0;
  }

  /* Ensure recent tables are properly scrollable */
  .recent-orders .admin-table,
  .recent-payouts .admin-table {
    min-width: 500px;
  }

  /* Improve table cell readability */
  .admin-table th,
  .admin-table td {
    padding: 0.875rem 0.75rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Add horizontal scroll indicators */
  .admin-table-container::before,
  .admin-table-container::after {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    pointer-events: none;
    z-index: 1;
  }

  .admin-table-container::before {
    content: '←';
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--primary-color);
    opacity: 0.7;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.5));
  }

  .admin-table-container::after {
    content: '→';
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--primary-color);
    opacity: 0.7;
    background: linear-gradient(to left, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.5));
  }

  /* Add subtle animation to indicate horizontal scrolling */
  .admin-table-container:not(:hover)::after {
    animation: pulse 1.5s infinite alternate;
  }

  @keyframes pulse {
    from { opacity: 0.5; }
    to { opacity: 0.9; }
  }
}

@media (max-width: 576px) {
  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .pending-vendors-list,
  .pending-riders-list {
    grid-template-columns: 1fr;
  }

  .document-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
  }

  .document-item img {
    height: 100px;
  }

  .document-expand-button {
    opacity: 1;
    transform: translateY(0);
    width: 1.75rem;
    height: 1.75rem;
    font-size: 0.75rem;
  }

  .admin-table-container {
    margin: 0 -1rem;
    padding: 0 1rem;
    width: calc(100% + 2rem);
    position: relative;
  }

  .admin-table {
    min-width: 500px;
    width: 100%;
  }

  /* Improve scroll visibility */
  .admin-table-container::after {
    width: 25px;
    opacity: 0.8;
  }

  .admin-title {
    font-size: 1.5rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.25rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .admin-user-avatar {
    width: 2rem;
    height: 2rem;
  }

  .search-input {
    width: 100%;
  }

  .table-header-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .add-button {
    width: 100%;
    justify-content: center;
  }

  .admin-nav-item {
    padding: 0.5rem 0.75rem;
  }

  .admin-nav-icon {
    margin-right: 0.5rem;
  }

  .dashboard-statistics,
  .dashboard-recent {
    grid-template-columns: 1fr;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.875rem;
  }

  .table-status {
    padding: 0.125rem 0.375rem;
    font-size: 0.7rem;
  }

  .customer-avatar {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.75rem;
  }

  .customer-name-cell {
    gap: 0.5rem;
  }

  .approve-button,
  .reject-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
}

/* Empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background-color: var(--bg-light);
  border-radius: var(--border-radius-md);
  text-align: center;
  margin: 1.5rem 0;
  box-shadow: var(--shadow-sm);
  border: 1px dashed var(--border-color);
}

.empty-icon {
  font-size: 4rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  opacity: 0.7;
  background-color: var(--primary-lighter);
  padding: 1.5rem;
  border-radius: 50%;
  box-shadow: 0 0 0 10px var(--primary-lighter);
}

.empty-state p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
  max-width: 80%;
  line-height: 1.6;
  font-weight: 500;
}

/* Customer table styles */
.customer-name-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.customer-avatar {
  width: 2rem;
  height: 2rem;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

/* Category table styles */
.category-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.category-icon {
  color: var(--primary-color);
}

/* Complaints and Suggestions */
.complaints-tab,
.suggestions-tab {
  margin-top: 1rem;
}

.description-cell {
  max-width: 300px;
  position: relative;
}

.view-image-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: var(--bg-light);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  color: var(--text-secondary);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-image-button:hover {
  background-color: var(--primary-color);
  color: white;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.pending {
  background-color: var(--warning-light);
  color: var(--warning-dark);
}

.status-badge.in_progress,
.status-badge.under_review {
  background-color: var(--info-light);
  color: var(--info-dark);
}

.status-badge.resolved,
.status-badge.implemented {
  background-color: var(--success-light);
  color: var(--success-dark);
}

.status-badge.rejected {
  background-color: rgba(117, 117, 117, 0.1);
  color: var(--status-rejected);
}

.action-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 0.5rem;
}

.in-progress-button {
  background-color: var(--info-light);
  color: var(--info-dark);
  border: 1px solid var(--info-dark);
}

.in-progress-button:hover {
  background-color: var(--info-color);
  color: white;
}

.resolve-button,
.implement-button {
  background-color: var(--success-light);
  color: var(--success-dark);
  border: 1px solid var(--success-dark);
}

.resolve-button:hover,
.implement-button:hover {
  background-color: var(--success-color);
  color: white;
}

.reject-button {
  background-color: var(--danger-light);
  color: var(--danger-dark);
  border: 1px solid var(--danger-dark);
}

.review-button {
  background-color: var(--info-light);
  color: var(--info-dark);
  border: 1px solid var(--info-dark);
}

.review-button:hover {
  background-color: var(--info-color);
  color: white;
}

.rating-display {
  display: flex;
  align-items: center;
}

.star {
  color: #ccc;
  margin-right: 2px;
}

.star.filled {
  color: #f8b400;
}

.table-actions {
  display: flex;
  gap: 0.5rem;
}

.edit-button,
.delete-button {
  background: none;
  border: none;
  width: 2rem;
  height: 2rem;
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.edit-button {
  color: var(--primary-color);
  background-color: rgba(248, 180, 0, 0.1);
}

.edit-button:hover {
  background-color: var(--info-color);
  color: white;
}

.delete-button:hover {
  background-color: var(--danger-color);
  color: white;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.add-button:hover {
  background-color: var(--primary-dark);
}

/* Approve and Reject buttons */
.approve-button,
.reject-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-sm);
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: var(--transition);
}

.approve-button:hover {
  background-color: var(--success-dark);
}

.reject-button:hover {
  background-color: var(--danger-color);
  color: white;
}

.table-search {
  display: flex;
  align-items: center;
  position: relative;
}

/* Table status indicators */
.table-status {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.price-cell .currency {
  color: var(--text-secondary);
  margin-right: 0.25rem;
  font-size: 0.875rem;
}

.price-cell .amount {
  color: var(--text-primary);
  font-weight: 600;
}

.status-pending {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.status-processing {
  background-color: var(--info-light);
  color: var(--info-color);
}

.status-confirmed {
  background-color: var(--info-light);
  color: var(--info-color);
}

.status-ready {
  background-color: rgba(156, 39, 176, 0.1);
  color: var(--status-ready);
}

.status-picked-up {
  background-color: rgba(255, 87, 34, 0.1);
  color: var(--status-picked-up);
}

.status-delivered {
  background-color: var(--success-light);
  color: var(--success-color);
}

.status-received {
  background-color: rgba(0, 150, 136, 0.1);
  color: var(--status-received);
}

.status-cancelled {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

.status-rejected {
  background-color: rgba(117, 117, 117, 0.1);
  color: var(--status-rejected);
}

.status-not_verified {
  background-color: rgba(255, 193, 7, 0.1);
  color: var(--warning-color);
}

.status-verified {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

/* Transfer Management Styles */
.transfers-tab {
  padding: 2rem;
}

.transfers-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--info-light);
  border: 1px solid var(--info-color);
  border-radius: var(--border-radius-md);
  margin-bottom: 2rem;
  color: var(--info-dark);
}

.transfers-info svg {
  font-size: 1.25rem;
  color: var(--info-color);
}

.table-header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  gap: 1rem;
}

.table-search {
  position: relative;
  flex: 1;
  max-width: 300px;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  font-size: 0.875rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: 0.875rem;
  transition: var(--transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.refresh-button:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.debug-button,
.create-transfer-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
}

.debug-button {
  background: var(--warning-color);
  color: white;
}

.debug-button:hover {
  background: var(--warning-dark);
  transform: scale(1.05);
}

.create-transfer-button {
  background: var(--success-color);
  color: white;
}

.create-transfer-button:hover {
  background: var(--success-dark);
  transform: scale(1.05);
}

/* Transfer Debug Modal */
.transfer-debug-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.debug-modal-content {
  background: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.debug-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-light);
}

.debug-modal-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

.close-debug-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: var(--danger-color);
  color: white;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: var(--transition);
}

.close-debug-button:hover {
  background: var(--danger-dark);
  transform: scale(1.05);
}

.debug-modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.debug-section {
  margin-bottom: 2rem;
}

.debug-section:last-child {
  margin-bottom: 0;
}

.debug-section h4 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-color);
}

.debug-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.debug-info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.debug-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.debug-value {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 500;
}

.debug-transfers-table {
  margin-top: 1rem;
}

.debug-transfers-table .admin-table {
  font-size: 0.875rem;
}

/* Status indicators for transfers */
.status-success {
  background: var(--success-light);
  color: var(--success-dark);
}

.status-failed {
  background: var(--danger-light);
  color: var(--danger-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-dashboard {
    flex-direction: column;
  }

  .admin-sidebar {
    position: fixed;
    top: 0;
    left: -280px;
    width: 280px;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease;
  }

  .admin-sidebar:not(.collapsed) {
    left: 0;
  }

  .admin-main {
    margin-left: 0;
    padding-top: 60px;
  }

  .admin-sidebar-toggle {
    display: flex;
    position: fixed;
    top: 15px;
    left: 15px;
    z-index: 1001;
    background: var(--primary-color);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-md);
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
  }

  .admin-sidebar-toggle:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
  }

  .admin-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }

  .admin-overlay.active {
    display: block;
  }

  .desktop-toggle {
    display: none;
  }

  .admin-header {
    padding: 1rem;
  }

  .admin-title {
    font-size: 1.5rem;
  }

  .admin-user {
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
  }

  .admin-user-info {
    text-align: right;
  }

  .admin-user-name {
    font-size: 0.875rem;
  }

  .admin-user-role {
    font-size: 0.75rem;
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .admin-table-container {
    overflow-x: auto;
  }

  .admin-table {
    min-width: 600px;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.5rem;
    font-size: 0.875rem;
  }

  .pending-vendor-card,
  .pending-rider-card {
    padding: 1rem;
  }

  .document-grid {
    grid-template-columns: 1fr;
  }

  .service-fee-form {
    grid-template-columns: 1fr;
  }

  .form-actions {
    justify-content: stretch;
  }

  .form-actions button {
    flex: 1;
  }

  /* Transfer management mobile styles */
  .transfers-tab {
    padding: 1rem;
  }

  .table-header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .table-search {
    max-width: none;
  }

  .debug-modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }

  .debug-modal-header {
    padding: 1rem;
  }

  .debug-modal-body {
    padding: 1rem;
  }

  .debug-info-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .debug-button,
  .create-transfer-button {
    width: 100%;
    height: 36px;
    font-size: 0.75rem;
  }
}
