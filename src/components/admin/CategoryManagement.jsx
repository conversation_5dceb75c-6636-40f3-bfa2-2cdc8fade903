import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import PropTypes from 'prop-types';
import {
  FaPlus, FaTag, FaEdit, FaTrash, FaSave, FaTimes,
} from 'react-icons/fa';
import { createCategory, updateCategory, deleteCategory } from '../../redux/slice/adminSlice';

const CategoryManagement = ({ categories }) => {
  const dispatch = useDispatch();
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [editingCategoryId, setEditingCategoryId] = useState(null);
  const [newCategory, setNewCategory] = useState({ name: '', description: '' });
  const [editCategory, setEditCategory] = useState({ name: '', description: '' });

  // Handle adding a new category
  const handleAddCategory = () => {
    setIsAddingCategory(true);
    setNewCategory({ name: '', description: '' });
  };

  // Handle saving a new category
  const handleSaveNewCategory = () => {
    if (!newCategory.name.trim()) {
      toast.error('Category name is required');
      return;
    }

    dispatch(createCategory(newCategory));
    setIsAddingCategory(false);
    setNewCategory({ name: '', description: '' });
  };

  // Handle editing a category
  const handleEditCategory = (category) => {
    setEditingCategoryId(category.id);
    setEditCategory({
      name: category.name,
      description: category.description || '',
    });
  };

  // Handle saving an edited category
  const handleSaveEditCategory = (id) => {
    if (!editCategory.name.trim()) {
      toast.error('Category name is required');
      return;
    }

    dispatch(updateCategory({ id, categoryData: editCategory }));
    setEditingCategoryId(null);
    setEditCategory({ name: '', description: '' });
  };

  // Handle deleting a category
  const handleDeleteCategory = (id) => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      dispatch(deleteCategory(id));
    }
  };

  // Handle canceling add/edit
  const handleCancel = () => {
    setIsAddingCategory(false);
    setEditingCategoryId(null);
  };

  return (
    <div className="categories-management">
      <div className="table-header-actions">
        {!isAddingCategory && (
          <button type="button" className="add-button" onClick={handleAddCategory}>
            <FaPlus />
            <span>Add Category</span>
          </button>
        )}
      </div>

      {isAddingCategory && (
        <div className="category-form">
          <h3>Add New Category</h3>
          <div className="form-group">
            <label htmlFor="categoryName">
              Category Name
              <input
                type="text"
                id="categoryName"
                value={newCategory.name}
                onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
                placeholder="Enter category name"
                required
              />
            </label>
          </div>
          <div className="form-group">
            <label htmlFor="categoryDescription">
              Description
              <textarea
                id="categoryDescription"
                value={newCategory.description}
                onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
                placeholder="Enter category description"
                rows="3"
              />
            </label>
          </div>
          <div className="form-actions">
            <button type="button" className="save-button" onClick={handleSaveNewCategory}>
              <FaSave />
              <span>Save</span>
            </button>
            <button type="button" className="cancel-button" onClick={handleCancel}>
              <FaTimes />
              <span>Cancel</span>
            </button>
          </div>
        </div>
      )}

      <table className="admin-table">
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Description</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {categories.map((category) => (
            <tr key={category.id}>
              <td>{category.id}</td>
              <td>
                {editingCategoryId === category.id ? (
                  <input
                    type="text"
                    value={editCategory.name}
                    onChange={(e) => setEditCategory({ ...editCategory, name: e.target.value })}
                    className="edit-input"
                  />
                ) : (
                  <div className="category-name">
                    <FaTag className="category-icon" />
                    {category.name}
                  </div>
                )}
              </td>
              <td>
                {editingCategoryId === category.id ? (
                  <textarea
                    value={editCategory.description}
                    onChange={(e) => setEditCategory({
                      ...editCategory,
                      description:
                      e.target.value,
                    })}
                    className="edit-textarea"
                    rows="2"
                  />
                ) : (
                  category.description
                )}
              </td>
              <td>
                <div className="table-actions">
                  {editingCategoryId === category.id ? (
                    <>
                      <button
                        type="button"
                        className="save-button"
                        onClick={() => handleSaveEditCategory(category.id)}
                        aria-label="Save changes"
                      >
                        <FaSave />
                        <span className="sr-only">Save</span>
                      </button>
                      <button
                        type="button"
                        className="cancel-button"
                        onClick={handleCancel}
                        aria-label="Cancel editing"
                      >
                        <FaTimes />
                        <span className="sr-only">Cancel</span>
                      </button>
                    </>
                  ) : (
                    <>
                      <button
                        type="button"
                        className="edit-button"
                        onClick={() => handleEditCategory(category)}
                        aria-label={`Edit ${category.name} category`}
                      >
                        <FaEdit />
                        <span className="sr-only">Edit</span>
                      </button>
                      <button
                        type="button"
                        className="delete-button"
                        onClick={() => handleDeleteCategory(category.id)}
                        aria-label={`Delete ${category.name} category`}
                      >
                        <FaTrash />
                        <span className="sr-only">Delete</span>
                      </button>
                    </>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

CategoryManagement.propTypes = {
  categories: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      name: PropTypes.string.isRequired,
      description: PropTypes.string,
    }),
  ).isRequired,
};

export default CategoryManagement;
