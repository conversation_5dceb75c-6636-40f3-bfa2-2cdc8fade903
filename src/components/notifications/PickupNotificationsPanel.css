.pickup-notifications-panel {
  position: fixed;
  top: 5rem;
  right: 1.25rem;
  width: 18.75rem;
  background-color: #2a2b2a;
  border-radius: 0.5rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.3);
  z-index: 1000;
  overflow: hidden;
  max-height: calc(100vh - 6.25rem);
  display: flex;
  flex-direction: column;
  animation: slide-in 0.3s ease-out;
}

@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.panel-header {
  background-color: #f8b400;
  color: #000;
  padding: 0.75rem 0.9375rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: bold;
}

.order-count {
  background-color: #fff;
  color: #000;
  border-radius: 50%;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.875rem;
}

.pickup-orders-list {
  padding: 0.625rem;
  overflow-y: auto;
  max-height: 25rem;
}

.pickup-order-item {
  background-color: #3a3b3a;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 0.625rem;
  border-left: 0.1875rem solid #f8b400;
}

.pickup-order-details h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #f8b400;
  font-size: 0.9375rem;
}

.pickup-order-details p {
  margin: 0.3125rem 0;
  font-size: 0.8125rem;
  color: #ddd;
}

.pickup-order-details strong {
  color: #fff;
}

.view-pickup-btn {
  background-color: #f8b400;
  color: #000;
  border: none;
  border-radius: 0.25rem;
  padding: 0.375rem 0.75rem;
  margin-top: 0.5rem;
  cursor: pointer;
  font-weight: bold;
  font-size: 0.8125rem;
  width: 100%;
  transition: background-color 0.3s ease;
}

.view-pickup-btn:hover {
  background-color: #e5a800;
}

.pickup-notification {
  padding: 0.625rem;
  background-color: #2a2b2a;
  border-radius: 0.5rem;
  border-left: 0.25rem solid #f8b400;
}

.pickup-notification h4 {
  color: #f8b400;
  margin-top: 0;
  margin-bottom: 0.625rem;
  font-size: 1.125rem;
}

.pickup-notification p {
  margin: 0.3125rem 0;
  color: #fff;
  font-size: 0.875rem;
}

.view-order-btn {
  background-color: #f8b400;
  color: #000;
  border: none;
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  margin-top: 0.625rem;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.view-order-btn:hover {
  background-color: #e5a800;
}

.Toastify__toast-container--top-center {
  top: 1.25rem;
  width: 25rem !important;
}

.Toastify__toast--info {
  background-color: #2a2b2a !important;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.3) !important;
}

.highlight-order {
  animation: highlight-pulse 2s ease-in-out;
  border: 0.1875rem solid #f8b400 !important;
  box-shadow: 0 0 1.25rem rgba(248, 180, 0, 0.7) !important;
  position: relative;
  z-index: 10;
}

@keyframes highlight-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(248, 180, 0, 0.6);
  }

  25% {
    transform: scale(1.03);
    box-shadow: 0 0 1.5625rem rgba(248, 180, 0, 0.9);
  }

  50% {
    transform: scale(1.01);
    box-shadow: 0 0 0.9375rem rgba(248, 180, 0, 0.7);
  }

  75% {
    transform: scale(1.02);
    box-shadow: 0 0 1.25rem rgba(248, 180, 0, 0.8);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(248, 180, 0, 0.6);
  }
}

/* Notifications Toggle Button Styles */
.notifications-toggle {
  position: fixed;
  top: 0.625rem;
  right: 0.625rem;
  z-index: 1002;
  cursor: move;
  user-select: none;
  touch-action: none;
  transition: opacity 0.3s ease;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 0.25rem;
  padding: 0.25rem;
  box-shadow: 0 0.125rem 0.3125rem rgba(0, 0, 0, 0.2);
}

.notifications-toggle.dragging {
  opacity: 0.7;
  cursor: grabbing;
}

.toggle-notifications-btn.draggable {
  cursor: grabbing;
  background-color: #e5a800;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.4);
  animation: pulse 1s infinite alternate;
}

.notifications-buttons {
  display: flex;
  align-items: center;
  gap: 0.3125rem;
}

.toggle-notifications-btn {
  background-color: #f8b400;
  color: #000;
  border: none;
  border-radius: 0.25rem;
  padding: 0.375rem 0.625rem;
  font-size: 0.75rem;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  box-shadow: 0 0.125rem 0.3125rem rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  opacity: 0.9;
}

.toggle-notifications-btn:hover {
  background-color: #e5a800;
  transform: translateY(-0.125rem);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.3);
  opacity: 1;
}

.reset-notifications-btn {
  background-color: #666;
  color: white;
  border: none;
  border-radius: 50%;
  width: 1.375rem;
  height: 1.375rem;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.reset-notifications-btn:hover {
  background-color: #555;
  opacity: 1;
  transform: rotate(180deg);
}

.notification-badge {
  border-radius: 50%;
  width: 1rem;
  height: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.625rem;
  margin-left: 0.375rem;
  font-weight: bold;
  transition: all 0.3s ease;
}

.has-notifications {
  background-color: #ff4d4f;
  color: white;
  animation: pulse 1.5s infinite;
}

.no-notifications {
  background-color: #999;
  color: #333;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7);
  }

  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 0.3125rem rgba(255, 77, 79, 0);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
  }
}

@media (max-width: 48rem) { /* 768px */
  .pickup-notifications-panel {
    width: 15.625rem;
    right: 0.625rem;
  }

  .notifications-toggle {
    top: 0.3125rem;
    right: 0.3125rem;
  }

  .notifications-buttons {
    gap: 0.1875rem;
  }

  .toggle-notifications-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.6875rem;
  }

  .reset-notifications-btn {
    width: 1.125rem;
    height: 1.125rem;
    font-size: 0.75rem;
  }

  .notification-badge {
    width: 0.875rem;
    height: 0.875rem;
    font-size: 0.5625rem;
    margin-left: 0.25rem;
  }
}

@media (max-width: 30rem) { /* 480px */
  .Toastify__toast-container--top-center {
    width: 90% !important;
  }

  .pickup-notifications-panel {
    width: 80%;
    right: 0.3125rem;
  }
}
