.mobile-notification-badge {
  position: fixed;
  z-index: 1000;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #ff000c 0%, #e60000 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 
    0 4px 20px rgba(255, 0, 12, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.mobile-notification-badge:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 6px 25px rgba(255, 0, 12, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.2);
}

.mobile-notification-badge:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

.mobile-notification-badge.active {
  background: linear-gradient(135deg, #0ebce9 0%, #0ea5e9 100%);
  box-shadow: 
    0 4px 20px rgba(14, 188, 233, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.15);
}

.mobile-notification-badge.animating {
  animation: newNotification 1s ease-out;
}

.badge-icon {
  position: relative;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bell-ring {
  animation: bellRing 0.5s ease-in-out;
}

.bell-normal {
  transition: transform 0.2s ease;
}

.mobile-notification-badge:hover .bell-normal {
  transform: scale(1.1);
}

.badge-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ffffff;
  color: #ff000c;
  border-radius: 50%;
  min-width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
  border: 2px solid #ff000c;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  animation: bounceIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.mobile-notification-badge.active .badge-count {
  background: #ffffff;
  color: #0ebce9;
  border-color: #0ebce9;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border: 3px solid rgba(255, 0, 12, 0.6);
  border-radius: 50%;
  animation: pulseRing 1s ease-out;
}

/* Animations */
@keyframes newNotification {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes bellRing {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-15deg);
  }
  75% {
    transform: rotate(15deg);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulseRing {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

/* Mobile-specific adjustments */
@media (max-width: 768px) {
  .mobile-notification-badge {
    width: 56px;
    height: 56px;
  }
  
  .badge-count {
    min-width: 22px;
    height: 22px;
    font-size: 11px;
    top: -4px;
    right: -4px;
  }
}

@media (max-width: 480px) {
  .mobile-notification-badge {
    width: 52px;
    height: 52px;
  }
  
  .badge-icon svg {
    width: 22px;
    height: 22px;
  }
  
  .badge-count {
    min-width: 20px;
    height: 20px;
    font-size: 10px;
    top: -3px;
    right: -3px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .mobile-notification-badge,
  .badge-count,
  .bell-ring,
  .pulse-ring {
    animation: none;
    transition: none;
  }
  
  .mobile-notification-badge:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .mobile-notification-badge {
    border: 2px solid #ffffff;
  }
  
  .badge-count {
    border-width: 3px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .mobile-notification-badge {
    box-shadow: 
      0 4px 20px rgba(255, 0, 12, 0.4),
      0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  .mobile-notification-badge.active {
    box-shadow: 
      0 4px 20px rgba(14, 188, 233, 0.4),
      0 2px 8px rgba(0, 0, 0, 0.3);
  }
}
