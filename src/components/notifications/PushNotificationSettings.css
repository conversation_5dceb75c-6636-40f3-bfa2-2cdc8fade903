.push-notification-settings {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin: 1rem 0;
  max-width: 600px;
}

.push-notification-header {
  margin-bottom: 1.5rem;
}

.push-notification-header h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.push-notification-header p {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

.push-notification-toggle {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.push-notification-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 1rem;
}

.push-notification-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.push-notification-enable {
  background-color: #f90;
  color: white;
}

.push-notification-enable:hover:not(:disabled) {
  background-color: #e68a00;
}

.push-notification-disable {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.push-notification-disable:hover:not(:disabled) {
  background-color: #eaeaea;
}

.push-notification-icon {
  margin-right: 0.5rem;
  width: 20px;
  height: 20px;
}

.push-notification-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  color: #666;
}

.push-notification-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-top-color: #f90;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.push-notification-error,
.push-notification-success {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.push-notification-error {
  background-color: #fff2f2;
  color: #d32f2f;
}

.push-notification-success {
  background-color: #f0fff4;
  color: #2e7d32;
}

.push-notification-error-icon,
.push-notification-success-icon {
  margin-right: 0.5rem;
  width: 18px;
  height: 18px;
}

.push-notification-not-supported {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.push-notification-not-supported h3 {
  margin-bottom: 0.5rem;
  color: #333;
}

.push-notification-info {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
}

.push-notification-info h4 {
  font-size: 1rem;
  margin-bottom: 0.75rem;
  color: #333;
}

.push-notification-info ul {
  padding-left: 1.5rem;
  color: #666;
}

.push-notification-info li {
  margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
  .push-notification-settings {
    padding: 1rem;
    margin: 0.75rem 0;
  }
}
