import { useState, useEffect } from 'react';
import { Bell, BellRing } from 'lucide-react';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import './MobileNotificationBadge.css';

/**
 * Mobile-specific notification badge component
 * Displays a floating notification badge on mobile devices
 */
const MobileNotificationBadge = ({ 
  onTogglePanel, 
  showPanel, 
  position = { bottom: '20px', right: '20px' },
  userRole = 'customer'
}) => {
  const orders = useSelector((state) => state.orders.orders);
  const [notificationCount, setNotificationCount] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      const isMobileDevice = window.innerWidth <= 768 || 
                           /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      setIsMobile(isMobileDevice);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Calculate notification count based on user role
  useEffect(() => {
    if (!orders || orders.length === 0) {
      setNotificationCount(0);
      return;
    }

    let count = 0;
    switch (userRole) {
      case 'vendor':
        count = orders.filter(order => order.status === 'confirmed').length;
        break;
      case 'rider':
        count = orders.filter(order => order.status === 'ready_for_pickup').length;
        break;
      case 'customer':
        count = orders.filter(order => order.status === 'delivered').length;
        break;
      default:
        count = 0;
    }

    // Trigger animation if count increased
    if (count > notificationCount) {
      setIsAnimating(true);
      setTimeout(() => setIsAnimating(false), 1000);
    }

    setNotificationCount(count);
  }, [orders, userRole, notificationCount]);

  // Don't render on desktop or if no notifications
  if (!isMobile) {
    return null;
  }

  const handleClick = () => {
    onTogglePanel();
    // Haptic feedback on mobile devices
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  };

  return (
    <div 
      className={`mobile-notification-badge ${isAnimating ? 'animating' : ''} ${showPanel ? 'active' : ''}`}
      style={position}
      onClick={handleClick}
      role="button"
      tabIndex={0}
      aria-label={`Notifications: ${notificationCount} unread`}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleClick();
        }
      }}
    >
      <div className="badge-icon">
        {notificationCount > 0 ? (
          <BellRing size={24} className="bell-ring" />
        ) : (
          <Bell size={24} className="bell-normal" />
        )}
      </div>
      
      {notificationCount > 0 && (
        <div className="badge-count">
          {notificationCount > 99 ? '99+' : notificationCount}
        </div>
      )}
      
      {/* Pulse animation for new notifications */}
      {isAnimating && (
        <div className="pulse-ring"></div>
      )}
    </div>
  );
};

MobileNotificationBadge.propTypes = {
  onTogglePanel: PropTypes.func.isRequired,
  showPanel: PropTypes.bool,
  position: PropTypes.object,
  userRole: PropTypes.string
};

export default MobileNotificationBadge;
