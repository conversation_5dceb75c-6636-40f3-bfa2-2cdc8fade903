import ApiUrl from '../helper-functions/ApiUrl';

// Get auth token directly from localStorage
const getAuthToken = () => {
  try {
    const authData = localStorage.getItem('authData');
    if (!authData) {
      return null;
    }

    const parsedData = JSON.parse(authData);

    // Check if token exists and is not undefined
    if (parsedData && parsedData.token && parsedData.token !== 'undefined') {
      // Return the token (which should include "Bearer " prefix)
      return parsedData.token;
    }

    return null;
  } catch (error) {
    // Silent fail if JSON parsing fails
    return null;
  }
};

// Check if the browser supports service workers and push notifications
export const isPushNotificationSupported = () => 'serviceWorker' in navigator && 'PushManager' in window;

// Request permission for push notifications
export const requestNotificationPermission = async () => {
  if (!isPushNotificationSupported()) {
    throw new Error('Push notifications are not supported in this browser');
  }

  try {
    const permission = await Notification.requestPermission();
    return permission === 'granted';
  } catch (error) {
    // In a production app, log to an error tracking service
    throw new Error(`Failed to request notification permission: ${error.message}`);
  }
};

// Wait for service worker to be ready (Vite PWA handles registration automatically)
export const waitForServiceWorker = async () => {
  if (!isPushNotificationSupported()) {
    throw new Error('Service workers are not supported in this browser');
  }

  try {
    // Wait for the service worker to be ready
    // Vite PWA automatically registers the service worker
    const registration = await navigator.serviceWorker.ready;
    return registration;
  } catch (error) {
    // In a production app, log to an error tracking service
    throw new Error(`Service worker not ready: ${error.message}`);
  }
};

// Get the VAPID public key from the server
export const getVapidPublicKey = async () => {
  try {
    const response = await fetch(`${ApiUrl}/push_subscriptions/vapid_public_key`);
    const data = await response.json();
    return data.vapid_public_key;
  } catch (error) {
    // In a production app, log to an error tracking service
    throw new Error(`Failed to get VAPID public key: ${error.message}`);
  }
};

// Convert a base64 string to a Uint8Array
const urlBase64ToUint8Array = (base64String) => {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; i += 1) {
    outputArray[i] = rawData.charCodeAt(i);
  }

  return outputArray;
};

// Subscribe to push notifications
export const subscribeToPushNotifications = async (vapidPublicKey) => {
  try {
    const registration = await navigator.serviceWorker.ready;
    let subscription = await registration.pushManager.getSubscription();

    if (!subscription) {
      subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(vapidPublicKey),
      });
    }

    return subscription;
  } catch (error) {
    // Add specific error handling
    if (error.name === 'NotAllowedError') {
      throw new Error('Notification permission denied');
    } else if (error.name === 'NotSupportedError') {
      throw new Error('Push notifications not supported');
    }
    throw new Error(`Failed to subscribe: ${error.message}`);
  }
};

// Save subscription to the server
export const saveSubscription = async (subscription, token) => {
  if (!subscription) {
    throw new Error('No subscription to save');
  }

  // Get token from parameter or localStorage
  let authToken = token;
  if (!authToken) {
    authToken = getAuthToken();
  }

  if (!authToken) {
    throw new Error('Authentication token not found');
  }

  try {
    // Extract subscription details
    const { endpoint, keys } = subscription.toJSON();

    // Send to server
    const response = await fetch(`${ApiUrl}/push_subscriptions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: authToken, // Token already includes "Bearer " prefix
      },
      body: JSON.stringify({
        subscription: {
          endpoint,
          p256dh: keys.p256dh,
          auth: keys.auth,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to save subscription: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    // In a production app, log to an error tracking service
    throw new Error(`Failed to save subscription: ${error.message}`);
  }
};

// Unsubscribe from push notifications
export const unsubscribeFromPushNotifications = async (token) => {
  if (!isPushNotificationSupported()) {
    throw new Error('Push notifications are not supported in this browser');
  }

  // Get token from parameter or localStorage
  let authToken = token;
  if (!authToken) {
    authToken = getAuthToken();
  }

  if (!authToken) {
    throw new Error('Authentication token not found');
  }

  try {
    const registration = await navigator.serviceWorker.ready;
    const subscription = await registration.pushManager.getSubscription();

    if (subscription) {
      // Unsubscribe locally
      await subscription.unsubscribe();

      const response = await fetch(`${ApiUrl}/push_subscriptions`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken, // Token already includes "Bearer " prefix
        },
        body: JSON.stringify({
          endpoint: subscription.endpoint,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to remove subscription from server: ${response.status}`);
      }

      return true;
    }

    return false;
  } catch (error) {
    // In a production app, log to an error tracking service
    throw new Error(`Failed to unsubscribe from push notifications: ${error.message}`);
  }
};

export const checkNotificationPermission = () => {
  if (!('Notification' in window)) {
    return 'not-supported';
  }
  return Notification.permission;
};

// Initialize push notifications
export const initializePushNotifications = async (token, vapidPublicKey) => {
  try {
    // Check if push notifications are supported
    if (!isPushNotificationSupported()) {
      throw new Error('Push notifications are not supported in this browser');
    }

    // Get auth token from localStorage if not provided as parameter
    let authToken = token;
    if (!authToken) {
      authToken = getAuthToken();
    }

    if (!authToken) {
      throw new Error('Authentication token not found');
    }

    // Request permission
    const permissionGranted = await requestNotificationPermission();
    if (!permissionGranted) {
      throw new Error('Notification permission denied');
    }

    // Wait for service worker to be ready (Vite PWA handles registration)
    await waitForServiceWorker();

    // Get VAPID public key if not provided
    const publicKey = vapidPublicKey || await getVapidPublicKey();

    // Subscribe to push notifications
    const subscription = await subscribeToPushNotifications(publicKey);

    // Save subscription to server with the auth token
    try {
      await saveSubscription(subscription, authToken);
    } catch (saveError) {
      // If saving to server fails, but subscription was created locally,
      // we should still consider this a partial success
      console.warn('⚠️ Push subscription created locally but failed to save to server:', saveError.message);
      // Don't throw here - let the caller check the actual subscription status
    }

    return true;
  } catch (error) {
    // In a production app, log to an error tracking service
    throw error; // Re-throw to let caller handle the specific error
  }
};
