import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import convertToLocalTime from '../helper-functions/convertToLocalTime';
import './PickupNotificationsPanel.css';

const PickupNotificationsPanel = ({ onViewOrder, viewedOrderIds }) => {
  const orders = useSelector((state) => state.orders.orders);
  const [readyForPickupOrders, setReadyForPickupOrders] = useState([]);
  const [ordersWithVendorInfo, setOrdersWithVendorInfo] = useState([]);

  // Determine if we're in a rider or vendor dashboard based on the URL
  const isRiderDashboard = window.location.pathname.includes('/rider');

  // Filter orders with appropriate status and exclude viewed orders
  useEffect(() => {
    if (orders && orders.length > 0) {
      // For riders, show ready_for_pickup orders
      // For vendors, show confirmed orders
      const statusToFilter = isRiderDashboard ? 'ready_for_pickup' : 'confirmed';

      const filteredOrders = orders.filter(
        (order) => order.status === statusToFilter && !viewedOrderIds.includes(order.id),
      );
      setReadyForPickupOrders(filteredOrders);
    }
  }, [orders, viewedOrderIds, isRiderDashboard]);

  // Merge vendor/customer information with orders based on dashboard type.
  useEffect(() => {
    if (readyForPickupOrders.length > 0) {
      const enhancedOrders = readyForPickupOrders.map((order) => {
        if (isRiderDashboard) {
          // For riders, add vendor information.
          let vendorInfo = null;

          // Priority 1: Check for direct vendor object (single vendor case)
          if (order.vendor) {
            vendorInfo = order.vendor;
          // Priority 2: Check for vendors array (multiple vendors case)
          } else if (order.vendors && order.vendors.length > 0) {
            // For pickup notifications, we only need the first vendor
            // since riders pick up from one vendor at a time
            const [firstVendor] = order.vendors;
            vendorInfo = firstVendor;
          }

          // Use vendorInfo if found
          return {
            ...order,
            vendor_name: vendorInfo?.name || 'Unknown vendor',
            vendor_address: vendorInfo?.address || 'Check vendor details',
            vendor_phone: vendorInfo?.phone || 'No phone available',
            display_type: 'vendor',
          };
        }

        // For vendors, determine whether to show recipient or customer details
        const hasRecipient = order.display_recipient;
        const recipientInfo = hasRecipient ? order.display_recipient : null;
        const customerInfo = order.customer || {};

        // Use recipient info if available, otherwise use customer info
        const displayInfo = recipientInfo || customerInfo;
        const isShowingRecipient = !!recipientInfo;

        return {
          ...order,
          display_name: displayInfo.name || (isShowingRecipient ? 'Unknown recipient' : 'Unknown customer'),
          display_phone: displayInfo.phone || 'No phone available',
          delivery_address: order.delivery_address || 'No delivery address provided',
          display_type: 'customer',
          is_recipient: isShowingRecipient,
        };
      });

      setOrdersWithVendorInfo(enhancedOrders);
    } else {
      // No orders available
      setOrdersWithVendorInfo([]);
    }
  }, [readyForPickupOrders, isRiderDashboard]);

  if (readyForPickupOrders.length === 0) {
    return null;
  }

  // Use ordersWithVendorInfo if available, otherwise use readyForPickupOrders
  const displayOrders = ordersWithVendorInfo.length > 0
    ? ordersWithVendorInfo
    : readyForPickupOrders;

  return (
    <div className="pickup-notifications-panel">
      <div className="panel-header">
        <h3>{isRiderDashboard ? 'Orders Ready for Pickup' : 'New Confirmed Orders'}</h3>
        <span className="order-count">{displayOrders.length}</span>
      </div>

      <div className="pickup-orders-list">
        {displayOrders.map((order) => (
          <div key={order.id} className="pickup-order-item">
            <div className="pickup-order-details">
              <h4>
                Order #
                {order.id}
              </h4>

              {/* Show different information based on dashboard type */}
              {order.display_type === 'vendor' ? (
                // Vendor information for riders
                <>
                  <p>
                    <strong>Vendor:</strong>
                    {' '}
                    {order.vendor_name || order.vendor?.name || 'Unknown vendor'}
                  </p>
                  <p>
                    <strong>Phone:</strong>
                    {' '}
                    {order.vendor_phone || order.vendor?.phone || 'No phone available'}
                  </p>
                  <p>
                    <strong>Address:</strong>
                    {' '}
                    {order.vendor_address || order.vendor?.address || 'Check vendor details'}
                  </p>
                  <p>
                    <strong>Ready since:</strong>
                    {' '}
                    {convertToLocalTime(order.updated_at)}
                  </p>
                </>
              ) : (
                // Customer information for vendors
                <>
                  <p>
                    <strong>{order.is_recipient ? 'Recipient:' : 'Customer:'}</strong>
                    {' '}
                    {order.display_name}
                  </p>
                  <p>
                    <strong>Phone:</strong>
                    {' '}
                    {order.display_phone}
                  </p>
                  <p>
                    <strong>Delivery Address:</strong>
                    {' '}
                    {order.delivery_address}
                  </p>
                  <p>
                    <strong>Order time:</strong>
                    {' '}
                    {convertToLocalTime(order.created_at)}
                  </p>
                </>
              )}
            </div>
            <button
              type="button"
              className="view-pickup-btn"
              onClick={() => onViewOrder(order.id)}
            >
              View Order
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

PickupNotificationsPanel.propTypes = {
  onViewOrder: PropTypes.func.isRequired,
  viewedOrderIds: PropTypes.arrayOf(PropTypes.number).isRequired,
};

export default PickupNotificationsPanel;
