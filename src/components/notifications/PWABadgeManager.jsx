import { useEffect, useCallback, useRef } from 'react';
import { useSelector } from 'react-redux';

/**
 * PWA Badge Manager - Handles app icon badge notifications for mobile devices
 * Shows notification count on the installed PWA app icon on mobile home screen
 * Uses the Badging API for supported browsers and falls back to document title updates
 */
const PWABadgeManager = () => {
  const orders = useSelector((state) => state.orders.orders);
  const authData = JSON.parse(localStorage.getItem('authData') || '{}');
  const userRole = authData.data?.role;
  const lastBadgeCountRef = useRef(0);
  const isInitializedRef = useRef(false);

  // Check if Badging API is supported
  const isBadgingSupported = useCallback(() => {
    return 'setAppBadge' in navigator && 'clearAppBadge' in navigator;
  }, []);

  // Get notification count based on user role
  const getNotificationCount = useCallback(() => {
    if (!orders || orders.length === 0) return 0;

    switch (userRole) {
      case 'vendor':
        // Count confirmed orders (new orders for vendors)
        return orders.filter(order => order.status === 'confirmed').length;
      
      case 'rider':
        // Count ready_for_pickup orders (new pickups for riders)
        return orders.filter(order => order.status === 'ready_for_pickup').length;
      
      case 'customer':
        // Count delivered orders waiting for confirmation
        return orders.filter(order => order.status === 'delivered').length;
      
      default:
        return 0;
    }
  }, [orders, userRole]);

  // Check if app is installed as PWA
  const isPWAInstalled = useCallback(() => {
    return window.matchMedia('(display-mode: standalone)').matches ||
           window.navigator.standalone === true ||
           document.referrer.includes('android-app://');
  }, []);

  // Check if device is Android
  const isAndroid = useCallback(() => {
    return /Android/i.test(navigator.userAgent);
  }, []);

  // Set app badge with count
  const setAppBadge = useCallback(async (count) => {
    try {
      const isAndroidDevice = isAndroid();

      // Android doesn't support Badging API - it uses push notifications for badges
      if (isAndroidDevice) {
        // On Android, badges are handled automatically by the system when push notifications are received
        // We'll still update the document title as a fallback
        updateDocumentTitle(count);
        return;
      }

      // Only set badge if PWA is installed and Badging API is supported (iOS, Windows, macOS)
      if (isPWAInstalled() && isBadgingSupported()) {
        if (count > 0) {
          await navigator.setAppBadge(count);
          lastBadgeCountRef.current = count;
        } else {
          await navigator.clearAppBadge();
          lastBadgeCountRef.current = 0;
        }
      } else {
        // Fallback: Update document title for non-PWA or unsupported browsers
        updateDocumentTitle(count);
      }
    } catch (error) {
      // Fallback to document title
      updateDocumentTitle(count);
    }
  }, [isBadgingSupported, isPWAInstalled, userRole, isAndroid]);

  // Fallback: Update document title with notification count
  const updateDocumentTitle = useCallback((count) => {
    const baseTitle = 'EaseFood Delivery';
    if (count > 0) {
      document.title = `(${count}) ${baseTitle}`;
    } else {
      document.title = baseTitle;
    }
  }, []);

  // Clear app badge
  const clearAppBadge = useCallback(async () => {
    try {
      if (isBadgingSupported()) {
        await navigator.clearAppBadge();
      }
      document.title = 'EaseFood Delivery';
    } catch (error) {
      console.warn('Failed to clear app badge:', error);
    }
  }, [isBadgingSupported]);

  // Initialize badge manager
  useEffect(() => {
    if (!isInitializedRef.current) {

      // Show Android-specific information.
      if (isAndroid()) {
        console.log('ℹ️ Android Note: Badge API not supported. Badges will appear automatically with push notifications.');
      }

      isInitializedRef.current = true;
    }
  }, [isPWAInstalled, isBadgingSupported, userRole, isAndroid]);

  // Update badge when orders change
  useEffect(() => {
    if (!userRole || !orders) return;

    const notificationCount = getNotificationCount();

    // Only update if count changed to avoid unnecessary API calls
    if (notificationCount !== lastBadgeCountRef.current) {
      setAppBadge(notificationCount);
    }
  }, [orders, getNotificationCount, setAppBadge, userRole]);

  // Clear badge when app becomes visible (user opens the app)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // App is now visible, optionally clear badge after a delay
        setTimeout(() => {
          const currentCount = getNotificationCount();
          if (currentCount === 0) {
            clearAppBadge();
          }
        }, 2000); // Clear after 2 seconds if no notifications
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [clearAppBadge, getNotificationCount]);

  // Handle page focus (when user switches back to the app)
  useEffect(() => {
    const handleFocus = () => {
      // Optionally reduce badge count or clear it
      const currentCount = getNotificationCount();
      setAppBadge(currentCount);
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [setAppBadge, getNotificationCount]);

  // Expose badge management functions globally for other components
  useEffect(() => {
    window.PWABadgeManager = {
      setAppBadge,
      clearAppBadge,
      getNotificationCount,
      isBadgingSupported: isBadgingSupported()
    };

    return () => {
      delete window.PWABadgeManager;
    };
  }, [setAppBadge, clearAppBadge, getNotificationCount, isBadgingSupported]);

  // This component doesn't render anything
  return null;
};

export default PWABadgeManager;
