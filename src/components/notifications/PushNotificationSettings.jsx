import { useState, useEffect } from 'react';
import {
  Bell, BellOff, AlertCircle, CheckCircle, Info, MessageSquare, ShoppingBag, Truck,
} from 'lucide-react';
import {
  isPushNotificationSupported,
  requestNotificationPermission,
  initializePushNotifications,
  unsubscribeFromPushNotifications,
} from './pushNotifications';
import './PushNotificationSettings.css';

// Helper function to get auth token from localStorage
const getAuthToken = () => {
  try {
    const authData = localStorage.getItem('authData');
    if (!authData) {
      return null;
    }

    const parsedData = JSON.parse(authData);

    if (parsedData && parsedData.token && parsedData.token !== 'undefined') {
      return parsedData.token;
    }

    return null;
  } catch (error) {
    return null;
  }
};

const PushNotificationSettings = () => {
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  const [notificationsSupported, setNotificationsSupported] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [subscriptionStatus, setSubscriptionStatus] = useState('unknown');

  // Check if notifications are supported and enabled on component mount.
  useEffect(() => {
    const checkNotificationStatus = async () => {
      const supported = isPushNotificationSupported();
      setNotificationsSupported(supported);

      if (supported) {
        const { permission } = Notification;
        setNotificationsEnabled(permission === 'granted');
        
        // Also check subscription status
        try {
          const registration = await navigator.serviceWorker.ready;
          const subscription = await registration.pushManager.getSubscription();
          setSubscriptionStatus(subscription ? 'subscribed' : 'not-subscribed');
          
          // Update notifications enabled based on both permission and subscription
          setNotificationsEnabled(permission === 'granted' && subscription !== null);
        } catch (error) {
          setSubscriptionStatus('error');
        }
      }
    };

    checkNotificationStatus();
  }, []);

  // Enable push notifications
  const enableNotifications = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Get the auth token
      const token = getAuthToken();

      if (!token) {
        setError('You need to be logged in to enable notifications.');
        setLoading(false);
        return;
      }

      // First, explicitly request notification permission
      const permissionGranted = await requestNotificationPermission();

      if (!permissionGranted) {
        setError('Notification permission denied. Please enable notifications in your browser settings.');
        setLoading(false);
        return;
      }

      // Then initialize push notifications with the token
      try {
        await initializePushNotifications(token);

        // Always re-check the actual subscription status after initialization attempt
        const registration = await navigator.serviceWorker.ready;
        const subscription = await registration.pushManager.getSubscription();
        const actuallyEnabled = Notification.permission === 'granted' && subscription !== null;

        setNotificationsEnabled(actuallyEnabled);
        setSubscriptionStatus(subscription ? 'subscribed' : 'not-subscribed');

        if (actuallyEnabled) {
          setSuccess('Push notifications enabled successfully!');
        } else {
          setError('Push notifications setup completed but subscription not found. Please try again.');
        }
      } catch (initError) {
        // Check if subscription was created despite the error
        try {
          const registration = await navigator.serviceWorker.ready;
          const subscription = await registration.pushManager.getSubscription();
          const actuallyEnabled = Notification.permission === 'granted' && subscription !== null;

          setNotificationsEnabled(actuallyEnabled);
          setSubscriptionStatus(subscription ? 'subscribed' : 'not-subscribed');

          if (actuallyEnabled) {
            setSuccess('Push notifications enabled successfully!');
          } else {
            setError(initError.message || 'Failed to enable push notifications. Please try again.');
          }
        } catch (statusError) {
          setError(initError.message || 'Failed to enable push notifications. Please try again.');
        }
      }
    } catch (err) {
      // Log error to error tracking service in production
      setError(err.message || 'Failed to enable push notifications');
    } finally {
      setLoading(false);
    }
  };

  // Disable push notifications
  const disableNotifications = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Get the auth token
      const token = getAuthToken();

      if (!token) {
        setError('You need to be logged in to disable notifications.');
        setLoading(false);
        return;
      }

      // Unsubscribe from push notifications with the token
      const result = await unsubscribeFromPushNotifications(token);

      if (result) {
        setNotificationsEnabled(false);
        setSuccess('Push notifications disabled successfully!');
      } else {
        setError('Failed to disable push notifications. Please try again.');
      }
    } catch (err) {
      // Log error to error tracking service in production
      setError(err.message || 'Failed to disable push notifications');
    } finally {
      setLoading(false);
    }
  };

  // Toggle notification details
  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  // If push notifications are not supported, show a message
  if (!notificationsSupported) {
    return (
      <div className="push-notification-card">
        <div className="push-notification-not-supported">
          <AlertCircle className="push-notification-icon" />
          <h3>Push Notifications Not Supported</h3>
          <p>
            Your browser does not support push notifications.
            Try using a modern browser like Chrome, Firefox, or Edge.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="push-notification-card">
      <div className="push-notification-header">
        <div className="push-notification-title">
          <Bell className="push-notification-title-icon" />
          <h3>Push Notifications</h3>
        </div>
        <p className="push-notification-subtitle">
          Stay updated with your orders and deliveries in real-time
        </p>
      </div>

      <div className="push-notification-status">
        <div className="push-notification-status-indicator">
          <div className={`status-dot ${notificationsEnabled ? 'active' : 'inactive'}`} />
          <span className="status-text">
            {notificationsEnabled ? 'Notifications are enabled' : 'Notifications are disabled'}
          </span>
        </div>
        {/* Add subscription status */}
          <div className="push-notification-subscription-status">
            <span className="subscription-status-text">
              Subscription: {subscriptionStatus === 'subscribed' ? 'Active' : 
                      subscriptionStatus === 'not-subscribed' ? 'Not Active' : 
                      subscriptionStatus === 'error' ? 'Error' : 'Checking...'}
            </span>
          </div>
      </div>

      <div className="push-notification-toggle">
        {notificationsEnabled ? (
          <button
            type="button"
            className="push-notification-button push-notification-disable"
            onClick={disableNotifications}
            disabled={loading}
          >
            <BellOff className="push-notification-button-icon" />
            <span>Turn Off Notifications</span>
          </button>
        ) : (
          <button
            type="button"
            className="push-notification-button push-notification-enable"
            onClick={enableNotifications}
            disabled={loading}
          >
            <Bell className="push-notification-button-icon" />
            <span>Turn On Notifications</span>
          </button>
        )}
      </div>

      {loading && (
        <div className="push-notification-loading">
          <div className="push-notification-spinner" />
          <span>Processing your request...</span>
        </div>
      )}

      {error && (
        <div className="push-notification-message push-notification-error">
          <AlertCircle className="push-notification-message-icon" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="push-notification-message push-notification-success">
          <CheckCircle className="push-notification-message-icon" />
          <span>{success}</span>
        </div>
      )}

      <div className="push-notification-details-toggle" onClick={toggleDetails} role="button" tabIndex={0} onKeyDown={(e) => e.key === 'Enter' && toggleDetails()}>
        <Info className="push-notification-details-icon" />
        <span>{showDetails ? 'Hide notification details' : 'Show notification details'}</span>
        <div className={`arrow-icon ${showDetails ? 'up' : 'down'}`} />
      </div>

      {showDetails && (
        <div className="push-notification-details">
          <h4>What You&apos;ll Receive:</h4>
          <ul className="push-notification-list">
            <li className="push-notification-list-item">
              <ShoppingBag className="push-notification-list-icon" />
              <div>
                <strong>Order Updates</strong>
                <p>Get notified when your order status changes</p>
              </div>
            </li>
            <li className="push-notification-list-item">
              <Truck className="push-notification-list-icon" />
              <div>
                <strong>Delivery Alerts</strong>
                <p>Know when your food is on the way and delivered</p>
              </div>
            </li>
            <li className="push-notification-list-item">
              <MessageSquare className="push-notification-list-icon" />
              <div>
                <strong>Important Messages</strong>
                <p>Receive important updates about your account</p>
              </div>
            </li>
          </ul>

          <div className="push-notification-privacy">
            <h4>Privacy Information:</h4>
            <p>
              We only use notifications to provide you with important updates about your orders
              and account.
              You can disable notifications at any time.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default PushNotificationSettings;
