import React, { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import axios from 'axios';
import {
  MessageSquare,
  Send,
  User,
  Mail,
  Tag,
  ThumbsUp,
  Star,
  Lightbulb,
  TrendingUp,
  Clock,
  MapPin,
} from 'lucide-react';
import useLocalStorage from '../hooks/useLocalStorage';
import ApiUrl from './helper-functions/ApiUrl';

const SuggestionsPage = () => {
  const { user } = useSelector((state) => state.auth);
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    category: 'menu',
    suggestion: '',
    rating: 0,
  });

  const [formErrors, setFormErrors] = useState({});
  // Add loading state to handle API calls
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submittedSuggestions, setSubmittedSuggestions] = useState([]);
  // Add state to track if we've loaded user suggestions
  const [loadedUserSuggestions, setLoadedUserSuggestions] = useState(false);

  // Use custom hook for auth data
  const [authData] = useLocalStorage('authData', null);

  // Function to fetch user's suggestions from the backend - wrapped in useCallback
  const fetchUserSuggestions = useCallback(async (userId) => {
    try {
      if (!authData) return;

      const response = await axios.get(
        `${ApiUrl}/customers/${userId}/suggestions`,
        {
          headers: {
            Authorization: authData.token,
          },
        },
      );

      // Format the suggestions for display
      const formattedSuggestions = response.data.map((suggestion) => ({
        id: suggestion.id,
        name: suggestion.name,
        category: suggestion.category,
        suggestion: suggestion.suggestion,
        rating: suggestion.rating,
        status: suggestion.status,
        date: new Date(suggestion.created_at).toISOString().split('T')[0],
      }));

      setSubmittedSuggestions(formattedSuggestions);
      setLoadedUserSuggestions(true);
    } catch (error) {
      // Silent fail - don't show error toast as this is a background operation
      // If needed, implement proper error logging service here
    }
  }, [authData]);

  // Update form data when user changes
  useEffect(() => {
    if (user) {
      setFormData((prev) => ({
        ...prev,
        name: user.name || '',
        email: user.email || '',
      }));

      // Load user's suggestions if they're logged in and we haven't loaded them yet
      if (user.id && !loadedUserSuggestions) {
        fetchUserSuggestions(user.id);
      }
    }
  }, [user, loadedUserSuggestions, authData, fetchUserSuggestions]);

  const categories = [
    { value: 'menu', label: 'Menu Items', icon: <Star size={16} /> },
    { value: 'delivery', label: 'Delivery Service', icon: <MapPin size={16} /> },
    { value: 'app', label: 'App Features', icon: <Lightbulb size={16} /> },
    { value: 'pricing', label: 'Pricing', icon: <TrendingUp size={16} /> },
    { value: 'timing', label: 'Operating Hours', icon: <Clock size={16} /> },
    { value: 'other', label: 'Other', icon: <MessageSquare size={16} /> },
  ];

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleRatingChange = (rating) => {
    setFormData((prev) => ({
      ...prev,
      rating,
    }));
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.suggestion.trim()) {
      errors.suggestion = 'Suggestion is required';
    } else if (formData.suggestion.trim().length < 10) {
      errors.suggestion = 'Suggestion should be at least 10 characters';
    }

    if (formData.rating === 0) {
      errors.rating = 'Please rate the importance of your suggestion';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Updated handleSubmit function to integrate with backend API
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      // Set submitting state to true to show loading indicator
      setIsSubmitting(true);

      try {
        // Get auth token if user is logged in
        const headers = {};

        if (authData) {
          headers.Authorization = authData.token;
        }

        // Send data to backend
        // Note: Backend expects snake_case keys, but frontend uses camelCase
        const response = await axios.post(
          `${ApiUrl}/suggestions`,
          {
            suggestion: {
              name: formData.name,
              email: formData.email,
              category: formData.category,
              suggestion: formData.suggestion,
              rating: formData.rating,
            },
          },
          {
            headers,
          },
        );

        // Create a new suggestion object for the UI with the response data
        const newSuggestion = {
          id: response.data.id,
          name: formData.name,
          category: formData.category,
          suggestion: formData.suggestion,
          rating: formData.rating,
          status: response.data.status || 'pending',
          date: new Date().toISOString().split('T')[0],
        };

        // Update the UI with the new suggestion at the top of the list
        setSubmittedSuggestions([newSuggestion, ...submittedSuggestions]);

        // Show success message
        toast.success('Your suggestion has been submitted successfully! Thank you for helping us improve.');

        // Reset form
        setFormData({
          name: user?.name || '',
          email: user?.email || '',
          category: 'menu',
          suggestion: '',
          rating: 0,
        });
      } catch (error) {
        // Extract error message from response if available
        const errorMessage = error.response?.data?.errors?.join(', ')
                            || error.response?.data?.error
                            || 'Failed to submit suggestion. Please try again later.';

        toast.error(errorMessage);
      } finally {
        // Reset submitting state
        setIsSubmitting(false);
      }
    }
  };

  const getCategoryIcon = (category) => {
    const found = categories.find((cat) => cat.value === category);
    return found ? found.icon : <MessageSquare size={16} />;
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'implemented':
        return 'status-implemented';
      case 'under_review':
        return 'status-review';
      case 'pending':
      default:
        return 'status-pending';
    }
  };

  return (
    <div className="suggestions-page">
      <div className="suggestions-header">
        <h1>Suggestions & Feedback</h1>
        <p>
          Help us improve EaseFood by sharing your ideas and feedback.
          We value your input and are constantly working to make our service better.
        </p>
      </div>

      <div className="suggestions-container">
        <div className="suggestions-form-container">
          <h2>
            <Lightbulb className="suggestions-icon" />
            Share Your Ideas
          </h2>
          <form className="suggestions-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="name">
                <div>
                  <User size={16} className="form-icon" />
                  Full Name
                  <span className="required">*</span>
                </div>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Your full name"
                />
                {formErrors.name && <p className="error-message">{formErrors.name}</p>}
              </label>
            </div>

            <div className="form-group">
              <label htmlFor="email">
                <div>
                  <Mail size={16} className="form-icon" />
                  Email Address
                  <span className="required">*</span>
                </div>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                />
                {formErrors.email && <p className="error-message">{formErrors.email}</p>}
              </label>
            </div>

            <div className="form-group">
              <label htmlFor="category">
                <div>
                  <Tag size={16} className="form-icon" />
                  Category
                  <span className="required">*</span>
                </div>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  className="form-select"
                >
                  {categories.map((category) => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </label>
            </div>

            <div className="form-group">
              <label htmlFor="suggestion">
                <div>
                  <MessageSquare size={16} className="form-icon" />
                  Your Suggestion
                  <span className="required">*</span>
                </div>
                <textarea
                  id="suggestion"
                  className="suggestion-textarea"
                  name="suggestion"
                  value={formData.suggestion}
                  onChange={handleChange}
                  placeholder="Please describe your suggestion in detail..."
                  rows="5"
                />
                {formErrors.suggestion && <p className="error-message">{formErrors.suggestion}</p>}
              </label>
            </div>

            <div className="form-group">
              <div>
                <ThumbsUp size={16} className="form-icon" />
                How important is this suggestion?
                <span className="required">*</span>
              </div>
              <div className="rating-container">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    aria-label={`Rate ${star} out of 5`}
                    type="button"
                    className={`rating-star ${formData.rating >= star ? 'active' : ''}`}
                    onClick={() => handleRatingChange(star)}
                  >
                    <Star size={24} />
                  </button>
                ))}
              </div>
              {formErrors.rating && <p className="error-message">{formErrors.rating}</p>}
            </div>

            {/* Add disabled state when form is submitting */}
            <button
              type="submit"
              className="submit-button"
              disabled={isSubmitting}
            >
              <Send size={16} />
              {isSubmitting ? 'Submitting...' : 'Submit Suggestion'}
            </button>
          </form>
        </div>

        <div className="submitted-suggestions">
          <h2>Recent Suggestions</h2>

          {submittedSuggestions.length === 0 ? (
            <p className="no-suggestions">No suggestions have been submitted yet. Be the first to share your ideas!</p>
          ) : (
            <div className="suggestions-list">
              {submittedSuggestions.map((item) => (
                <div key={item.id} className="suggestion-card">
                  <div className="suggestion-header">
                    <div className="suggestion-category">
                      {getCategoryIcon(item.category)}
                      <span>{categories.find((cat) => cat.value === item.category)?.label || 'Other'}</span>
                    </div>
                    <div className={`suggestion-status ${getStatusBadgeClass(item.status)}`}>
                      {item.status.replace('_', ' ')}
                    </div>
                  </div>

                  <div className="suggestion-content">
                    <p>{item.suggestion}</p>
                  </div>

                  <div className="suggestion-footer">
                    <div className="suggestion-user">
                      <User size={14} />
                      <span>{item.name}</span>
                    </div>
                    <div className="suggestion-date">
                      <Clock size={14} />
                      <span>{item.date}</span>
                    </div>
                    <div className="suggestion-rating">
                      {/* Rendering stars without using map to avoid key issues */}
                      {item.rating >= 1 && <Star size={14} className="filled" />}
                      {item.rating >= 2 && <Star size={14} className="filled" />}
                      {item.rating >= 3 && <Star size={14} className="filled" />}
                      {item.rating >= 4 && <Star size={14} className="filled" />}
                      {item.rating >= 5 && <Star size={14} className="filled" />}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SuggestionsPage;
