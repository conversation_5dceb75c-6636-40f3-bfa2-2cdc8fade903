import { useState, useEffect } from 'react';
import { X, ChevronLeft, ChevronRight, SkipForward } from 'lucide-react';
import './DashboardTutorial.css';

const DashboardTutorial = ({ 
  isVisible, 
  onClose, 
  onSkip, 
  tutorialSteps, 
  className = '' 
}) => {
  const [currentStep, setCurrentStep] = useState(0);

  // Reset to first step when tutorial becomes visible.
  useEffect(() => {
    if (isVisible) {
      setCurrentStep(0);
    }
  }, [isVisible]);

  // Highlight target element when step changes
  useEffect(() => {
    // Remove any existing highlights first
    document.querySelectorAll('.tutorial-highlight').forEach(el => {
      el.classList.remove('tutorial-highlight');
    });

    if (isVisible && tutorialSteps[currentStep]?.targetSelector) {
      const targetElement = document.querySelector(tutorialSteps[currentStep].targetSelector);
      if (targetElement) {
        // Add highlight class
        targetElement.classList.add('tutorial-highlight');
        
        // Scroll to element with offset for better visibility
        setTimeout(() => {
          targetElement.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center',
            inline: 'center'
          });
        }, 100);
      }
    }

    // Cleanup function
    return () => {
      document.querySelectorAll('.tutorial-highlight').forEach(el => {
        el.classList.remove('tutorial-highlight');
      });
    };
  }, [currentStep, isVisible, tutorialSteps]);

  const handleNext = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleClose();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleClose = () => {
    // Remove any existing highlights
    document.querySelectorAll('.tutorial-highlight').forEach(el => {
      el.classList.remove('tutorial-highlight');
    });
    onClose();
  };

  const handleSkip = () => {
    // Remove any existing highlights
    document.querySelectorAll('.tutorial-highlight').forEach(el => {
      el.classList.remove('tutorial-highlight');
    });
    onSkip();
  };

  if (!isVisible) return null;

  const currentStepData = tutorialSteps[currentStep];
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === tutorialSteps.length - 1;

  return (
    <>
      {/* Overlay */}
      <div className="tutorial-overlay" onClick={handleSkip} />
      
      {/* Tutorial Modal */}
      <div className={`tutorial-modal ${className}`}>
        {/* Header */}
        <div className="tutorial-header">
          <div className="tutorial-progress">
            <span className="tutorial-step-counter">
              {currentStep + 1} of {tutorialSteps.length}
            </span>
            <div className="tutorial-progress-bar">
              <div 
                className="tutorial-progress-fill"
                style={{ width: `${((currentStep + 1) / tutorialSteps.length) * 100}%` }}
              />
            </div>
          </div>
          <button
            type="button"
            className="tutorial-close-btn"
            onClick={handleSkip}
            aria-label="Close tutorial"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="tutorial-content">
          {currentStepData?.icon && (
            <div className="tutorial-icon">
              {currentStepData.icon}
            </div>
          )}
          
          <h3 className="tutorial-title">{currentStepData?.title}</h3>
          
          <p className="tutorial-description">{currentStepData?.description}</p>
          
          {currentStepData?.details && (
            <div className="tutorial-details">
              {currentStepData.details}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="tutorial-footer">
          <button
            type="button"
            className="tutorial-skip-btn"
            onClick={handleSkip}
          >
            <SkipForward size={16} />
            Skip Tutorial
          </button>
          
          <div className="tutorial-navigation">
            <button
              type="button"
              className={`tutorial-nav-btn ${isFirstStep ? 'disabled' : ''}`}
              onClick={handlePrevious}
              disabled={isFirstStep}
            >
              <ChevronLeft size={16} />
              Previous
            </button>
            
            <button
              type="button"
              className="tutorial-next-btn"
              onClick={handleNext}
            >
              {isLastStep ? 'Finish' : 'Next'}
              {!isLastStep && <ChevronRight size={16} />}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default DashboardTutorial; 