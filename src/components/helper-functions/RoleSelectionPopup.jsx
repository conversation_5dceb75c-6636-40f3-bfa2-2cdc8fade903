import React from 'react';
import PropTypes from 'prop-types';
import {
  User,
  ShoppingBag,
  Truck,
  Shield,
} from 'react-feather';

const RoleSelectionPopup = ({ onClose, onSelect, isLogin }) => {
  const actionType = isLogin ? 'login' : 'signup';
  const title = isLogin ? 'Login' : 'Sign Up';

  const getRoleDescription = (role) => {
    switch (role) {
      case 'customer':
        return 'Order delicious food from local vendors';
      case 'vendor':
        return 'Sell your food and manage your business';
      case 'rider':
        return 'Deliver food and earn money';
      case 'admin':
        return 'Manage the entire platform';
      default:
        return '';
    }
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'customer':
        return <User />;
      case 'vendor':
        return <ShoppingBag />;
      case 'rider':
        return <Truck />;
      case 'admin':
        return <Shield />;
      default:
        return <User />;
    }
  };

  return (
    <div className="popup-overlay">
      <div className="popup-content">
        <div className="popup-header">
          <h2 className="popup-title">{title}</h2>
          <p className="popup-subtitle">Select your role to continue</p>
          <button
            className="auth-close"
            type="button"
            onClick={onClose}
            aria-label="Close"
          >
            ×
          </button>
        </div>

        <div className="popup-body">
          <div className="role-buttons-container">
            <button
              type="button"
              className="role-button"
              onClick={() => onSelect('customer', actionType)}
            >
              <div className="role-icon">{getRoleIcon('customer')}</div>
              <div className="role-info">
                <h3 className="role-name">Customer</h3>
                <p className="role-description">{getRoleDescription('customer')}</p>
              </div>
            </button>

            <button
              type="button"
              className="role-button"
              onClick={() => onSelect('vendor', actionType)}
            >
              <div className="role-icon">{getRoleIcon('vendor')}</div>
              <div className="role-info">
                <h3 className="role-name">Vendor</h3>
                <p className="role-description">{getRoleDescription('vendor')}</p>
              </div>
            </button>

            <button
              type="button"
              className="role-button"
              onClick={() => onSelect('rider', actionType)}
            >
              <div className="role-icon">{getRoleIcon('rider')}</div>
              <div className="role-info">
                <h3 className="role-name">Rider</h3>
                <p className="role-description">{getRoleDescription('rider')}</p>
              </div>
            </button>

            {isLogin && (
              <button
                type="button"
                className="role-button"
                onClick={() => onSelect('admin', actionType)}
              >
                <div className="role-icon">{getRoleIcon('admin')}</div>
                <div className="role-info">
                  <h3 className="role-name">Admin</h3>
                  <p className="role-description">{getRoleDescription('admin')}</p>
                </div>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

RoleSelectionPopup.propTypes = {
  onClose: PropTypes.func.isRequired,
  onSelect: PropTypes.func.isRequired,
  isLogin: PropTypes.bool.isRequired,
};

export default RoleSelectionPopup;
