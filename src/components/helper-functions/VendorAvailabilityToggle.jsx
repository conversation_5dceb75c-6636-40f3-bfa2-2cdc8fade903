import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import { toast } from 'react-toastify';
// import { Store, StoreX } from 'lucide-react';
import * as Icons from 'lucide-react';
import {
  updateVendorAvailability,
  selectCurrentVendor,
  selectAvailabilityStatus,
  selectAvailabilityError,
  clearAvailabilityError,
  setCurrentVendor,
} from '../../redux/slice/vendersSlice';
import './VendorAvailabilityToggle.css';

const VendorAvailabilityToggle = ({ user }) => {
  const dispatch = useDispatch();
  const currentVendor = useSelector(selectCurrentVendor);
  const availabilityStatus = useSelector(selectAvailabilityStatus);
  const availabilityError = useSelector(selectAvailabilityError);

  // Initialize vendor data from user prop if not in state
  useEffect(() => {
    if (user && !currentVendor) {
      dispatch(setCurrentVendor(user));
    }
  }, [user, currentVendor, dispatch]);

  // Handle availability error
  useEffect(() => {
    if (availabilityError) {
      toast.error(`Failed to update availability: ${availabilityError}`);
      dispatch(clearAvailabilityError());
    }
  }, [availabilityError, dispatch]);

  const handleToggleAvailability = async () => {
    if (!currentVendor || availabilityStatus === 'loading') return;

    const newAvailability = !currentVendor.is_available;

    try {
      await dispatch(updateVendorAvailability({
        vendorId: currentVendor.id,
        isAvailable: newAvailability,
      })).unwrap();

      toast.success(
        newAvailability
          ? 'You are now available for orders!'
          : 'You are now unavailable for orders',
      );
    } catch (error) {
      // Error is handled by the useEffect above
      throw new Error('Toggle availability error:', error);
    }
  };

  if (!currentVendor) {
    return null;
  }

  const OpenIcon = Icons.Store || (() => <span aria-hidden>🏬</span>);
  const CloseIcon = Icons.StoreX || Icons.X || Icons.XCircle || (() => <span aria-hidden>✕</span>);

  return (
    <button
      type="button"
      className={`restaurant-status-btn ${currentVendor.is_available ? 'available' : 'unavailable'}`}
      onClick={handleToggleAvailability}
      disabled={availabilityStatus === 'loading'}
      title={`Restaurant is ${currentVendor.is_available ? 'Available' : 'Unavailable'} - Click to ${currentVendor.is_available ? 'close' : 'open'}`}
      aria-label={`${currentVendor.is_available ? 'Disable' : 'Enable'} restaurant availability`}
    >
      {currentVendor.is_available ? <OpenIcon size={20} /> : <CloseIcon size={20} />}
      <span>{currentVendor.is_available ? 'Open' : 'Closed'}</span>
    </button>
  );
};

VendorAvailabilityToggle.propTypes = {
  user: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    name: PropTypes.string,
    email: PropTypes.string,
    is_available: PropTypes.bool,
  }).isRequired,
};

export default VendorAvailabilityToggle;
