import { useState } from 'react';
import PropTypes from 'prop-types';
import { toast } from 'react-toastify';
import { useDispatch, useSelector } from 'react-redux';
import { addFood, selectFoodsStatus } from '../../redux/slice/foodsSlice';

const AddFoodForm = ({ vendorId = 5 }) => {
  const dispatch = useDispatch();
  const status = useSelector(selectFoodsStatus);

  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [priceMode, setPriceMode] = useState('single'); // 'single' or 'multiple'
  const [singlePrice, setSinglePrice] = useState('');
  const [multiplePrices, setMultiplePrices] = useState([
    { label: 'Small', price: '' },
    { label: 'Medium', price: '' },
    { label: 'Large', price: '' },
  ]);
  const [foodImage, setFoodImage] = useState(null);
  const [isAdding, setIsAdding] = useState(false);

  const addPriceOption = () => {
    setMultiplePrices([...multiplePrices, { label: '', price: '' }]);
  };

  const removePriceOption = (index) => {
    if (multiplePrices.length > 1) {
      setMultiplePrices(multiplePrices.filter((_, i) => i !== index));
    }
  };

  const updatePriceOption = (index, field, value) => {
    const updated = [...multiplePrices];
    updated[index][field] = value;
    setMultiplePrices(updated);
  };

  const validateForm = () => {
    if (!name.trim() || !description.trim() || !foodImage) {
      toast.error('Please fill in all required fields');
      return false;
    }

    if (priceMode === 'single') {
      if (!singlePrice || singlePrice <= 0) {
        toast.error('Please enter a valid price');
        return false;
      }
    } else {
      const validPrices = multiplePrices.filter((p) => p.label.trim() && p.price > 0);
      if (validPrices.length === 0) {
        toast.error('Please add at least one valid price option');
        return false;
      }
    }

    return true;
  };

  const calculateDisplayPrice = (price) => (parseFloat(price) * 1.10).toFixed(2); // 10% service fee

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!validateForm()) return;

    const formData = new FormData();
    formData.append('food[name]', name);
    formData.append('food[description]', description);
    formData.append('food[vendor_id]', vendorId);
    formData.append('food[food_image]', foodImage);

    if (priceMode === 'single') {
      formData.append('food[price]', singlePrice);
    } else {
      const validPrices = multiplePrices
        .filter((p) => p.label.trim() && p.price > 0)
        .map((p) => parseFloat(p.price));

      validPrices.forEach((price) => {
        formData.append('food[prices][]', price);
      });
    }

    setIsAdding(true);
    try {
      await dispatch(addFood({ vendorId, foodData: formData })).unwrap();
      toast.success('Food added successfully!');

      // Reset form
      setName('');
      setDescription('');
      setSinglePrice('');
      setMultiplePrices([
        { label: 'Small', price: '' },
        { label: 'Medium', price: '' },
        { label: 'Large', price: '' },
      ]);
      setFoodImage(null);
    } catch (error) {
      const errorMessage = error?.message || 'Unknown error';
      toast.error(`Failed to add food: ${errorMessage}`);
    } finally {
      setIsAdding(false);
    }
  };

  return (
    <form className="add-food-form" onSubmit={handleSubmit}>
      <h3>Add New Food</h3>
      <div>
        <input
          type="text"
          placeholder="Food Name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          required
        />
      </div>
      <div>
        <textarea
          value={description}
          placeholder="Description"
          onChange={(e) => setDescription(e.target.value)}
          required
        />
      </div>
      {/* Price Mode Selection */}
      <div className="price-mode-selection">
        <h4>Pricing Options</h4>
        <div>
          <label htmlFor="single-price-mode">
            <input
              type="radio"
              value="single"
              checked={priceMode === 'single'}
              onChange={(e) => setPriceMode(e.target.value)}
            />
            Single Price
          </label>
          <label htmlFor="multiple-price-mode">
            <input
              type="radio"
              value="multiple"
              checked={priceMode === 'multiple'}
              onChange={(e) => setPriceMode(e.target.value)}
            />
            Multiple Sizes/Options
          </label>
        </div>
      </div>

      {/* Single Price Mode */}
      {priceMode === 'single' && (
        <div className="single-price-section">
          <div id="price-notice">
            <strong>Note:</strong>
            A 10% service fee will be added to the price you set.
            <p>
              For example, if you set a price of GH₵50, customers will see GH₵55.00,
              and you&apos;ll receive GH₵50 when they order.
            </p>
          </div>
          <input
            type="number"
            placeholder="Price (GH₵)"
            value={singlePrice}
            onChange={(e) => setSinglePrice(e.target.value)}
            required
          />
          {singlePrice && !isNaN(Number(singlePrice)) && Number(singlePrice) > 0 && (
            <div className="price-preview">
              Customer will see: GH₵
              {calculateDisplayPrice(singlePrice)}
            </div>
          )}
        </div>
      )}

      {/* Multiple Prices Mode */}
      {priceMode === 'multiple' && (
        <div className="multiple-prices-section">
          <div id="price-notice">
            <strong>Note:</strong>
            A 10% service fee will be added to each price you set.
          </div>

          {multiplePrices.map((priceOption, index) => (
            <div key={index} className="price-option">
              <input
                type="text"
                placeholder="Size/Option (e.g., Small, Medium, Large)"
                value={priceOption.label}
                onChange={(e) => updatePriceOption(index, 'label', e.target.value)}
              />
              <input
                type="number"
                placeholder="Price (GH₵)"
                value={priceOption.price}
                onChange={(e) => updatePriceOption(index, 'price', e.target.value)}
              />
              {priceOption.price && !isNaN(Number(priceOption.price))
              && Number(priceOption.price) > 0 && (
                <div className="price-preview">
                  Customer sees: GH₵
                  {calculateDisplayPrice(priceOption.price)}
                </div>
              )}
              {multiplePrices.length > 1 && (
                <button
                  type="button"
                  onClick={() => removePriceOption(index)}
                  className="remove-price-btn"
                >
                  Remove
                </button>
              )}
            </div>
          ))}

          <button
            type="button"
            onClick={addPriceOption}
            className="add-price-btn"
          >
            Add Another Option
          </button>
        </div>
      )}
      <div>
        <h3>Food Image</h3>
        <input
          id="foodImage"
          type="file"
          onChange={(e) => setFoodImage(e.target.files[0])}
          aria-labelledby="foodImage"
        />
      </div>
      <button type="submit" disabled={isAdding || status === 'loading'}>
        {isAdding ? 'Adding Food...' : 'Add Food'}
      </button>
    </form>
  );
};

AddFoodForm.propTypes = {
  vendorId: PropTypes.number.isRequired,
};

export default AddFoodForm;
