/* Restaurant Status Button - Compact design matching help button */
.restaurant-status-btn {
  position: absolute;
  top: 1rem;
  left: 1rem;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  z-index: 10;
  color: white;
}

.restaurant-status-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.restaurant-status-btn.available {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  box-shadow: 0 2px 8px rgba(56, 161, 105, 0.3);
}

.restaurant-status-btn.unavailable {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  box-shadow: 0 2px 8px rgba(197, 48, 48, 0.3);
}

.restaurant-status-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.restaurant-status-btn.available:hover:not(:disabled) {
  box-shadow: 0 4px 12px rgba(56, 161, 105, 0.4);
}

.restaurant-status-btn.unavailable:hover:not(:disabled) {
  box-shadow: 0 4px 12px rgba(197, 48, 48, 0.4);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .restaurant-status-btn {
    top: 0.5rem;
    left: 0.5rem;
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .restaurant-status-btn.available {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    box-shadow: 0 2px 8px rgba(56, 161, 105, 0.4);
  }

  .restaurant-status-btn.unavailable {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    box-shadow: 0 2px 8px rgba(197, 48, 48, 0.4);
  }
}