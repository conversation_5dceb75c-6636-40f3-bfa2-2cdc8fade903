import { useState, useEffect, useCallback } from 'react';
import { RotateCcw } from 'lucide-react';
import './AutoRefresh.css';

const AutoRefresh = ({ 
  onRefresh, 
  interval = 600000, // 10 minutes in milliseconds
  disabled = false,
  className = ''
}) => {
  const [timeLeft, setTimeLeft] = useState(interval);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Handle manual refresh
  const handleManualRefresh = useCallback(async () => {
    if (isRefreshing || disabled) return;
    
    setIsRefreshing(true);
    try {
      await onRefresh();
      setTimeLeft(interval); // Reset timer after manual refresh
    } catch (error) {
      console.error('Refresh failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [onRefresh, interval, isRefreshing, disabled]);

  // Auto-refresh timer
  useEffect(() => {
    if (disabled || isRefreshing) return;

    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1000) {
          // Time to refresh - do it silently in background
          handleManualRefresh();
          return interval;
        }
        return prev - 1000;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [disabled, isRefreshing, handleManualRefresh, interval]);

  // Calculate progress percentage
  const progressPercentage = ((interval - timeLeft) / interval) * 100;

  return (
    <div className={`auto-refresh-container ${className}`}>
      {/* Main refresh button */}
      <button
        className={`auto-refresh-button ${isRefreshing ? 'refreshing' : ''}`}
        onClick={handleManualRefresh}
        disabled={isRefreshing || disabled}
        title={isRefreshing ? 'Refreshing...' : 'Click to refresh now'}
      >
        <div className="refresh-icon-container">
          <RotateCcw 
            size={20} 
            className={`refresh-icon ${isRefreshing ? 'spinning' : ''}`} 
          />
          {!isRefreshing && (
            <svg className="progress-ring" width="44" height="44">
              <circle
                className="progress-ring-background"
                cx="22"
                cy="22"
                r="18"
                fill="transparent"
                stroke="currentColor"
                strokeWidth="2"
                opacity="0.2"
              />
              <circle
                className="progress-ring-progress"
                cx="22"
                cy="22"
                r="18"
                fill="transparent"
                stroke="currentColor"
                strokeWidth="2"
                strokeDasharray={`${2 * Math.PI * 18}`}
                strokeDashoffset={`${2 * Math.PI * 18 * (1 - progressPercentage / 100)}`}
                transform="rotate(-90 22 22)"
              />
            </svg>
          )}
        </div>
      </button>

      {/* Status indicator - only show when refreshing */}
      {isRefreshing && (
        <div className="refresh-status">
          <span className="status-text">Refreshing...</span>
        </div>
      )}
    </div>
  );
};

export default AutoRefresh;
