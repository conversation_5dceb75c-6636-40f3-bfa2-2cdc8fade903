import React, { useState, useEffect } from 'react';
import { MapPin, AlertTriangle, X } from 'lucide-react';

const LocationAlert = () => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Add a class to prevent scrolling when modal is open
    if (isVisible) {
      document.body.classList.add('modal-open');
    } else {
      document.body.classList.remove('modal-open');
    }

    return () => {
      document.body.classList.remove('modal-open');
    };
  }, [isVisible]);

  const handleClose = () => {
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <div className="location-alert-overlay">
      <div className="location-alert-modal">
        <button
          type="button"
          className="location-alert-close"
          onClick={handleClose}
          aria-label="Close alert"
        >
          <X size={24} />
        </button>

        <div className="location-alert-icon">
          <AlertTriangle size={40} />
        </div>

        <h2 className="location-alert-title">Service Area Notice</h2>

        <div className="location-alert-content">
          <p className="location-alert-main">
            <strong>EaseFood currently operates only in Dunkwa-On-Offin and its localities.</strong>
          </p>
          <p className="location-alert-secondary">
            If you are not within this area, please only use this system to order food for someone
            who is within our service area.
          </p>
        </div>

        <div className="location-alert-badge">
          <MapPin size={18} />
          <span>Dunkwa-On-Offin, Ghana</span>
        </div>

        <button
          type="button"
          className="location-alert-button"
          onClick={handleClose}
        >
          I Understand
        </button>
      </div>
    </div>
  );
};

export default LocationAlert;
