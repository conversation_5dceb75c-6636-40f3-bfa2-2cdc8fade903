.ios-install-banner {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ff9900;
  color: white;
  padding: 16px;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.ios-install-content {
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 600px;
  margin: 0 auto;
}

.ios-install-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ios-install-text {
  flex: 1;
  min-width: 0;
}

.ios-install-text h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.ios-install-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.4;
}

.share-icon {
  display: inline-block;
  font-size: 16px;
  margin: 0 2px;
  padding: 2px 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.ios-install-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.ios-show-instructions {
  background: white;
  color: #ff9900;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ios-show-instructions:hover {
  background: #f0f0f0;
}

.ios-dismiss-button {
  background: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ios-dismiss-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.ios-install-instructions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.instruction-step {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 14px;
}

.instruction-step span:first-child {
  background: rgba(255, 255, 255, 0.2);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 12px;
  flex-shrink: 0;
}

/* Responsive design */
@media (max-width: 480px) {
  .ios-install-content {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
  
  .ios-install-buttons {
    width: 100%;
    justify-content: center;
  }
  
  .ios-show-instructions {
    flex: 1;
    max-width: 150px;
  }
}