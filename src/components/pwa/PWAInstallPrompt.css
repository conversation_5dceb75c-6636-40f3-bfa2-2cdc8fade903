.pwa-install-prompt {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  max-width: 400px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translate(-50%, 100%);
    opacity: 0;
  }

  to {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}

.pwa-install-content {
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.pwa-install-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
}

.pwa-install-icon img {
  width: 64px;
  height: 64px;
  border-radius: 12px;
}

.pwa-install-text {
  text-align: center;
  margin-bottom: 16px;
}

.pwa-install-text h3 {
  margin: 0 0 8px;
  font-size: 18px;
  color: #333;
}

.pwa-install-text p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.pwa-install-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.pwa-install-button {
  padding: 10px 20px;
  background-color: #f90;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.pwa-install-button:hover {
  background-color: #e68a00;
}

.pwa-dismiss-button {
  padding: 10px 20px;
  background-color: transparent;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.pwa-dismiss-button:hover {
  background-color: #f5f5f5;
}

@media (max-width: 480px) {
  .pwa-install-prompt {
    width: 95%;
  }
}
