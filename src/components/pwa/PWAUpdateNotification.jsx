import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import './PWAUpdateNotification.css';

const PWAUpdateNotification = () => {
  const [showUpdateNotification, setShowUpdateNotification] = useState(false);
  const [updateSW, setUpdateSW] = useState(null);

  useEffect(() => {
    // Import and setup VitePWA update handler
    import('virtual:pwa-register').then(({ registerSW }) => {
      const updateSW = registerSW({
        onNeedRefresh() {
          // Show update notification when new content is available
          setShowUpdateNotification(true);
        },
        onOfflineReady() {
          // Handle offline ready state if needed
          toast.info('App is now ready to work offline 🎉');
        },
        onRegisterError(error) {
          console.log('SW registration error', error);
        },
        immediate: true,
      });

      setUpdateSW(() => updateSW);
    });

    // Listen for service worker updates
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        // The new service worker has taken control, reload the page
        window.location.reload();
      });
    }
  }, []);

  const handleUpdateClick = () => {
    if (updateSW) {
      // First, send skip waiting message to the service worker
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: 'SKIP_WAITING'
        });
      }

      // Trigger the update
      updateSW(true);
      setShowUpdateNotification(false);

      // Show loading message
      toast.info('Updating app... Please wait');
    }
  };

  const handleDismissClick = () => {
    setShowUpdateNotification(false);
  };

  if (!showUpdateNotification) return null;

  return (
    <div className="pwa-update-notification">
      <div className="pwa-update-content">
        <div className="pwa-update-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M21 12a9 9 0 0 0-9-9 9 9 0 0 0-9 9" />
            <polyline points="9 3 9 9 15 9" />
            <path d="M3 12a9 9 0 0 0 9 9 9 9 0 0 0 9-9" />
            <polyline points="15 21 15 15 9 15" />
          </svg>
        </div>
        <div className="pwa-update-text">
          <h3>Update Available</h3>
          <p>A new version of EaseFood is available. Update now for the latest features!</p>
        </div>
        <div className="pwa-update-buttons">
          <button type="button" className="pwa-update-button" onClick={handleUpdateClick}>
            Update Now
          </button>
          <button type="button" className="pwa-dismiss-button" onClick={handleDismissClick}>
            Later
          </button>
        </div>
      </div>
    </div>
  );
};

export default PWAUpdateNotification;
