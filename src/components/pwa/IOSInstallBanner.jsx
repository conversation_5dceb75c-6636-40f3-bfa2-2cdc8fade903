import { useState, useEffect } from 'react';
import './iOSInstallBanner.css';

const IOSInstallBanner = () => {
  const [showBanner, setShowBanner] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);

  useEffect(() => {
    // Detect iOS devices
    const detectIOS = () => /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

    // Check if already running as PWA (standalone mode)
    const detectStandalone = () => window.navigator.standalone === true
             || window.matchMedia('(display-mode: standalone)').matches;

    const iOS = detectIOS();
    const standalone = detectStandalone();

    setIsIOS(iOS);
    setIsStandalone(standalone);

    // Show banner only for iOS users who haven't installed the app
    if (iOS && !standalone) {
      // Check if user has dismissed the banner before
      const dismissed = localStorage.getItem('ios-install-banner-dismissed');
      if (!dismissed) {
        setShowBanner(true);
      }
    }
  }, []);

  const handleDismiss = () => {
    setShowBanner(false);
    // Remember user's choice for 30 days
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + 30);
    localStorage.setItem('ios-install-banner-dismissed', expirationDate.toISOString());
  };

  const handleInstallClick = () => {
    setShowInstructions(!showInstructions);
  };

  if (!showBanner || !isIOS || isStandalone) {
    return null;
  }

  return (
    <div className="ios-install-banner">
      <div className="ios-install-content">
        <div className="ios-install-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <rect x="5" y="2" width="14" height="20" rx="2" ry="2" />
            <line x1="12" y1="18" x2="12.01" y2="18" />
          </svg>
        </div>
        <div className="ios-install-text">
          <h3>Install EaseFood App</h3>
          <p>
            For the best experience (and to get order notifications),
            add EaseFood to your home screen: Tap
            {' '}
            <strong>Share</strong>
            {' '}
            <span className="share-icon">⎙</span>
            {' '}
            then
            {' '}
            <strong>&quot;Add to Home Screen&quot;</strong>
          </p>
        </div>
        <div className="ios-install-buttons">
          <button
            type="button"
            className="ios-show-instructions"
            onClick={handleInstallClick}
          >
            {showInstructions ? 'Hide Instructions' : 'Show Me How'}
          </button>
          <button
            type="button"
            className="ios-dismiss-button"
            onClick={handleDismiss}
          >
            ✕
          </button>
        </div>
      </div>

      {/* Installation instructions modal */}
      {showInstructions && (
        <div className="ios-install-instructions">
          <div className="instruction-step">
            <span>1.</span>
            {' '}
            Tap the Share button
            <span className="share-icon">⎙</span>
            {' '}
            at the bottom of Safari
          </div>
          <div className="instruction-step">
            <span>2.</span>
            {' '}
            Scroll down and tap &quot;Add to Home Screen&quot;
          </div>
          <div className="instruction-step">
            <span>3.</span>
            {' '}
            Tap &quot;Add&quot; to install EaseFood
          </div>
        </div>
      )}
    </div>
  );
};

export default IOSInstallBanner;
