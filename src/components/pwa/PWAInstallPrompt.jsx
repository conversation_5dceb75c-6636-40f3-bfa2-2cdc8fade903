import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import './PWAInstallPrompt.css';

const PWAInstallPrompt = () => {
  const [installPromptEvent, setInstallPromptEvent] = useState(null);
  const [isAppInstalled, setIsAppInstalled] = useState(false);
  const [showPrompt, setShowPrompt] = useState(false);

  useEffect(() => {
    // Check if the app is already installed
    if (window.matchMedia('(display-mode: standalone)').matches) {
      setIsAppInstalled(true);
    }

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (event) => {
      // Prevent the default browser prompt
      event.preventDefault();
      // Save the event for later use
      setInstallPromptEvent(event);
      // Show our custom prompt
      setShowPrompt(true);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    // Listen for app installed event
    window.addEventListener('appinstalled', () => {
      setIsAppInstalled(true);
      setShowPrompt(false);
    });

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = () => {
    if (!installPromptEvent) return;

    // Show the browser's install prompt
    installPromptEvent.prompt();

    // Wait for the user to respond to the prompt
    installPromptEvent.userChoice.then((choiceResult) => {
      if (choiceResult.outcome === 'accepted') {
        toast.success('User accepted the install prompt');
        setIsAppInstalled(true);
      } else {
        toast.error('User dismissed the install prompt');
      }
      // Clear the saved prompt event
      setInstallPromptEvent(null);
      setShowPrompt(false);
    });
  };

  const handleDismissClick = () => {
    setShowPrompt(false);
  };

  if (!showPrompt || isAppInstalled) return null;

  return (
    <div className="pwa-install-prompt">
      <div className="pwa-install-content">
        <div className="pwa-install-icon">
          <img src="/apple-touch-icon.png" alt="EaseFood" />
        </div>
        <div className="pwa-install-text">
          <h3>Install EaseFood App</h3>
          <p>Install our app for a better experience!</p>
        </div>
        <div className="pwa-install-buttons">
          <button type="button" className="pwa-install-button" onClick={handleInstallClick}>
            Install
          </button>
          <button type="button" className="pwa-dismiss-button" onClick={handleDismissClick}>
            Not Now
          </button>
        </div>
      </div>
    </div>
  );
};

export default PWAInstallPrompt;
