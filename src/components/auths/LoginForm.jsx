import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { toast } from 'react-toastify';
import {
  <PERSON>aEye,
  FaEyeSlash,
  FaUser,
  FaLock,
  FaUtensils,
  FaMotorcycle,
  FaStore,
  FaUserShield,
} from 'react-icons/fa';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { login } from '../../redux/slice/authSlice';

const LoginForm = ({ role = '', type = '' }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {
    loading, error, isAuthenticated, user,
  } = useSelector((state) => state.auth);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (type === 'login') {
      dispatch(login({ role, credentials: { username, password } }));
    }
  };

  const handleTogglePassword = () => {
    setShowPassword((prevState) => !prevState);
  };

  useEffect(() => {
    if (isAuthenticated) {
      toast.success('Login successful!');

      // Redirect based on role.
      if (user?.role === 'customer') {
        navigate('/');
      } else if (user?.role === 'rider') {
        navigate('/riders-dashboard');
      } else if (user?.role === 'vendor') {
        navigate('/vendors-dashboard');
      } else if (user?.role === 'admin') {
        navigate('/admin-dashboard');
      }
    }
    return undefined;
  }, [isAuthenticated, user, navigate]);

  // Get role-specific icon
  const getRoleIcon = () => {
    switch (role) {
      case 'rider':
        return <FaMotorcycle className="role-icon" />;
      case 'vendor':
        return <FaStore className="role-icon" />;
      case 'admin':
        return <FaUserShield className="role-icon" />;
      default:
        return <FaUtensils className="role-icon" />;
    }
  };

  // Get role-specific welcome message
  const getWelcomeMessage = () => {
    switch (role) {
      case 'rider':
        return 'Ready to deliver delicious meals to hungry customers? Log in to start your delivery journey.';
      case 'vendor':
        return 'Welcome back to your food business dashboard. Log in to manage your menu and orders.';
      case 'admin':
        return 'Welcome to the admin dashboard. Log in to manage the EaseFood platform.';
      default:
        return 'Your favorite dishes are just a few clicks away. Log in to order fresh and delicious meals, delivered straight to your door.';
    }
  };

  return (
    <div className="auth-page">
      <div className="auth-form">
        <div className="auth-form-header">
          <h2>
            {getRoleIcon()}
            Welcome Back to EaseFood
          </h2>
          <div className="role-badge">
            {role === 'customer' ? 'Customer Login' : ''}
            {role === 'vendor' ? 'Food Vendor Login' : ''}
            {role === 'rider' ? 'Delivery Rider Login' : ''}
            {role === 'admin' ? 'Admin Login' : ''}
          </div>
          <p className="message">{getWelcomeMessage()}</p>
        </div>

        <div className="auth-form-content">
          {error && (
            <div className="error-message">
              <FaEyeSlash style={{ marginRight: '0.5rem' }} />
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="form-field">
              <label htmlFor="login-username" className="form-label">
                Username
                <span className="required">*</span>
                <div className="input-container">
                  <FaUser className="input-icon" />
                  <input
                    id="login-username"
                    type="text"
                    name="username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    placeholder="Enter your username"
                    className="form-input"
                    required
                  />
                </div>
              </label>
            </div>

            <div className="form-field">
              <label htmlFor="login-password" className="form-label">
                Password
                <span className="required">*</span>
                <div className="input-container">
                  <FaLock className="input-icon" />
                  <input
                    id="login-password"
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    className="form-input"
                    required
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={handleTogglePassword}
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                  >
                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
              </label>
            </div>

            <button
              type="submit"
              className="nav-button submit-button login-button"
              disabled={loading}
              aria-label="Login to your account"
            >
              {loading ? 'Logging in...' : 'Login'}
            </button>
          </form>

          {/* Forgot Password Link */}
          <div className="forgot-password-link">
            <button
              type="button"
              onClick={() => navigate('/forgot-password', { state: { role } })}
            >
              Forgot your password?
            </button>
          </div>
        </div>

        <div className="auth-form-footer">
          <p className="account-link">
            Don&apos;t have an account yet?
            {' '}
            <a href="/signup">Sign up now</a>
            {' '}
            to experience the tastiest delivery service in town.
          </p>
        </div>
      </div>
    </div>
  );
};

LoginForm.propTypes = {
  role: PropTypes.string.isRequired,
  type: PropTypes.string.isRequired,
};

export default LoginForm;
