import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { toast } from 'react-toastify';
import {
  FaEye,
  FaEyeSlash,
  FaLock,
  FaUtensils,
  FaMotorcycle,
  FaStore,
  FaUserShield,
  FaCheckCircle,
} from 'react-icons/fa';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { resetPassword, clearResetPasswordState } from '../../redux/slice/authSlice';

const ResetPasswordForm = ({ role = '' }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [password, setPassword] = useState('');
  const [passwordConfirmation, setPasswordConfirmation] = useState('');
  const [passwordError, setPasswordError] = useState('');
  
  const [searchParams] = useSearchParams();
  const resetToken = searchParams.get('reset_password_token');
  
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {
    resetPasswordLoading,
    resetPasswordSuccess,
    error,
  } = useSelector((state) => state.auth);

  // Validate passwords match
  useEffect(() => {
    if (password && passwordConfirmation) {
      if (password !== passwordConfirmation) {
        setPasswordError('Passwords do not match');
      } else {
        setPasswordError('');
      }
    } else {
      setPasswordError('');
    }
  }, [password, passwordConfirmation]);

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!resetToken) {
      toast.error('Invalid or missing reset token');
      return;
    }

    if (!password.trim()) {
      toast.error('Please enter a new password');
      return;
    }

    if (password !== passwordConfirmation) {
      toast.error('Passwords do not match');
      return;
    }

    if (password.length < 6) {
      toast.error('Password must be at least 6 characters long');
      return;
    }

    dispatch(resetPassword({
      role,
      resetToken,
      password,
      passwordConfirmation
    }));
  };

  const handleTogglePassword = () => {
    setShowPassword((prevState) => !prevState);
  };

  const handleToggleConfirmPassword = () => {
    setShowConfirmPassword((prevState) => !prevState);
  };

  const handleBackToLogin = () => {
    dispatch(clearResetPasswordState());
    navigate('/login', { state: { role } });
  };

  useEffect(() => {
    if (resetPasswordSuccess) {
      toast.success('Password reset successfully! You can now login with your new password.');
      setTimeout(() => {
        handleBackToLogin();
      }, 2000);
    }
  }, [resetPasswordSuccess]);

  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  // Clean up state when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearResetPasswordState());
    };
  }, [dispatch]);

  // Check if reset token is present
  useEffect(() => {
    if (!resetToken) {
      toast.error('Invalid or missing reset token. Please request a new password reset.');
      navigate('/forgot-password', { state: { role } });
    }
  }, [resetToken, navigate, role]);

  // Get role-specific icon
  const getRoleIcon = () => {
    switch (role) {
      case 'rider':
        return <FaMotorcycle className="role-icon" />;
      case 'vendor':
        return <FaStore className="role-icon" />;
      case 'admin':
        return <FaUserShield className="role-icon" />;
      default:
        return <FaUtensils className="role-icon" />;
    }
  };

  // Get role-specific message
  const getRoleMessage = () => {
    switch (role) {
      case 'rider':
        return 'Create a new secure password for your rider account.';
      case 'vendor':
        return 'Create a new secure password for your vendor account.';
      case 'admin':
        return 'Create a new secure password for your admin account.';
      default:
        return 'Create a new secure password for your customer account.';
    }
  };

  if (resetPasswordSuccess) {
    return (
      <div className="auth-page">
        <div className="auth-form">
          <div className="auth-form-header">
            <h2>
              <FaCheckCircle className="role-icon" style={{ color: '#28a745' }} />
              Password Reset Successful
            </h2>
            <div className="role-badge">
              {role === 'customer' ? 'Customer Password Reset' : ''}
              {role === 'vendor' ? 'Vendor Password Reset' : ''}
              {role === 'rider' ? 'Rider Password Reset' : ''}
              {role === 'admin' ? 'Admin Password Reset' : ''}
            </div>
            <p className="message">
              Your password has been successfully reset! You will be redirected to the login page shortly.
            </p>
          </div>

          <div className="auth-form-content">
            <button
              type="button"
              className="nav-button submit-button login-button"
              onClick={handleBackToLogin}
            >
              Continue to Login
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-page">
      <div className="auth-form">
        <div className="auth-form-header">
          <h2>
            {getRoleIcon()}
            Set New Password
          </h2>
          <div className="role-badge">
            {role === 'customer' ? 'Customer Password Reset' : ''}
            {role === 'vendor' ? 'Vendor Password Reset' : ''}
            {role === 'rider' ? 'Rider Password Reset' : ''}
            {role === 'admin' ? 'Admin Password Reset' : ''}
          </div>
          <p className="message">{getRoleMessage()}</p>
        </div>

        <div className="auth-form-content">
          <form onSubmit={handleSubmit}>
            <div className="form-field">
              <label htmlFor="new-password" className="form-label">
                New Password
                <span className="required">*</span>
                <div className="input-container">
                  <FaLock className="input-icon" />
                  <input
                    id="new-password"
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your new password"
                    className="form-input"
                    required
                    minLength={6}
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={handleTogglePassword}
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                  >
                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
              </label>
            </div>

            <div className="form-field">
              <label htmlFor="confirm-password" className="form-label">
                Confirm New Password
                <span className="required">*</span>
                <div className="input-container">
                  <FaLock className="input-icon" />
                  <input
                    id="confirm-password"
                    type={showConfirmPassword ? 'text' : 'password'}
                    name="passwordConfirmation"
                    value={passwordConfirmation}
                    onChange={(e) => setPasswordConfirmation(e.target.value)}
                    placeholder="Confirm your new password"
                    className="form-input"
                    required
                    minLength={6}
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={handleToggleConfirmPassword}
                    aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                  >
                    {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
                {passwordError && (
                  <p className="error-message">
                    <FaEyeSlash style={{ marginRight: '0.5rem' }} />
                    {passwordError}
                  </p>
                )}
              </label>
            </div>

            <button
              type="submit"
              className="nav-button submit-button login-button"
              disabled={resetPasswordLoading || passwordError}
              aria-label="Reset password"
            >
              {resetPasswordLoading ? 'Resetting...' : 'Reset Password'}
            </button>
          </form>
        </div>

        <div className="auth-form-footer">
          <p className="account-link">
            Remember your password?
            {' '}
            <button
              type="button"
              onClick={handleBackToLogin}
              style={{
                background: 'none',
                border: 'none',
                color: '#f8b400',
                textDecoration: 'underline',
                cursor: 'pointer',
                padding: 0,
                font: 'inherit'
              }}
            >
              Back to Login
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

ResetPasswordForm.propTypes = {
  role: PropTypes.string.isRequired,
};

export default ResetPasswordForm;
