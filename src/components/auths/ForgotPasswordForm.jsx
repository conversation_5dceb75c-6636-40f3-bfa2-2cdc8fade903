import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { toast } from 'react-toastify';
import {
  FaEnvelope,
  FaUtensils,
  FaMotorcycle,
  FaStore,
  FaUserShield,
  FaArrowLeft,
  FaCheckCircle,
} from 'react-icons/fa';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { forgotPassword, clearForgotPasswordState } from '../../redux/slice/authSlice';

const ForgotPasswordForm = ({ role = '' }) => {
  const [email, setEmail] = useState('');
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {
    forgotPasswordLoading,
    forgotPasswordSuccess,
    error,
  } = useSelector((state) => state.auth);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!email.trim()) {
      toast.error('Please enter your email address');
      return;
    }
    dispatch(forgotPassword({ role, email }));
  };

  const handleBackToLogin = () => {
    dispatch(clearForgotPasswordState());
    navigate('/login', { state: { role } });
  };

  useEffect(() => {
    if (forgotPasswordSuccess) {
      toast.success('Password reset instructions have been sent to your email!');
    }
  }, [forgotPasswordSuccess]);

  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  // Clean up state when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearForgotPasswordState());
    };
  }, [dispatch]);

  // Get role-specific icon
  const getRoleIcon = () => {
    switch (role) {
      case 'rider':
        return <FaMotorcycle className="role-icon" />;
      case 'vendor':
        return <FaStore className="role-icon" />;
      case 'admin':
        return <FaUserShield className="role-icon" />;
      default:
        return <FaUtensils className="role-icon" />;
    }
  };

  // Get role-specific message
  const getRoleMessage = () => {
    switch (role) {
      case 'rider':
        return 'Enter your email address and we\'ll send you instructions to reset your rider account password.';
      case 'vendor':
        return 'Enter your email address and we\'ll send you instructions to reset your vendor account password.';
      case 'admin':
        return 'Enter your email address and we\'ll send you instructions to reset your admin account password.';
      default:
        return 'Enter your email address and we\'ll send you instructions to reset your customer account password.';
    }
  };

  if (forgotPasswordSuccess) {
    return (
      <div className="auth-page">
        <div className="auth-form">
          <div className="auth-form-header">
            <h2>
              <FaCheckCircle className="role-icon" style={{ color: '#28a745' }} />
              Check Your Email
            </h2>
            <div className="role-badge">
              {role === 'customer' ? 'Customer Password Reset' : ''}
              {role === 'vendor' ? 'Vendor Password Reset' : ''}
              {role === 'rider' ? 'Rider Password Reset' : ''}
              {role === 'admin' ? 'Admin Password Reset' : ''}
            </div>
            <p className="message">
              We've sent password reset instructions to <strong>{email}</strong>.
              Please check your email and follow the instructions to reset your password.
            </p>
            <p className="message" style={{ fontSize: '0.9rem', color: '#666' }}>
              If you don't see the email in your inbox, please check your spam folder.
              The reset link will expire in 6 hours for security reasons.
            </p>
          </div>

          <div className="auth-form-content">
            <button
              type="button"
              className="nav-button submit-button"
              onClick={handleBackToLogin}
              style={{ 
                backgroundColor: '#6c757d',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.5rem'
              }}
            >
              <FaArrowLeft />
              Back to Login
            </button>
          </div>

          <div className="auth-form-footer">
            <p className="account-link">
              Didn't receive the email?
              {' '}
              <button
                type="button"
                onClick={() => {
                  dispatch(clearForgotPasswordState());
                  setEmail('');
                }}
                style={{
                  background: 'none',
                  border: 'none',
                  color: '#f8b400',
                  textDecoration: 'underline',
                  cursor: 'pointer',
                  padding: 0,
                  font: 'inherit'
                }}
              >
                Try again
              </button>
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-page">
      <div className="auth-form">
        <div className="auth-form-header">
          <h2>
            {getRoleIcon()}
            Reset Your Password
          </h2>
          <div className="role-badge">
            {role === 'customer' ? 'Customer Password Reset' : ''}
            {role === 'vendor' ? 'Vendor Password Reset' : ''}
            {role === 'rider' ? 'Rider Password Reset' : ''}
            {role === 'admin' ? 'Admin Password Reset' : ''}
          </div>
          <p className="message">{getRoleMessage()}</p>
        </div>

        <div className="auth-form-content">
          <form onSubmit={handleSubmit}>
            <div className="form-field">
              <label htmlFor="forgot-email" className="form-label">
                Email Address
                <span className="required">*</span>
                <div className="input-container">
                  <FaEnvelope className="input-icon" />
                  <input
                    id="forgot-email"
                    type="email"
                    name="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email address"
                    className="form-input"
                    required
                  />
                </div>
              </label>
            </div>

            <button
              type="submit"
              className="nav-button submit-button login-button"
              disabled={forgotPasswordLoading}
              aria-label="Send reset instructions"
            >
              {forgotPasswordLoading ? 'Sending...' : 'Send Reset Instructions'}
            </button>
          </form>
        </div>

        <div className="auth-form-footer">
          <p className="account-link">
            Remember your password?
            {' '}
            <button
              type="button"
              onClick={handleBackToLogin}
              style={{
                background: 'none',
                border: 'none',
                color: '#f8b400',
                textDecoration: 'underline',
                cursor: 'pointer',
                padding: 0,
                font: 'inherit'
              }}
            >
              Back to Login
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

ForgotPasswordForm.propTypes = {
  role: PropTypes.string.isRequired,
};

export default ForgotPasswordForm;
