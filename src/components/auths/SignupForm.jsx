import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { toast } from 'react-toastify';
import {
  FaEye,
  FaEyeSlash,
  FaUser,
  FaPhone,
  FaEnvelope,
  FaLock,
  FaUserTag,
  FaMapMarkerAlt,
  FaIdCard,
  FaMoneyBillWave,
  FaUtensils,
  FaMotorcycle,
  FaCalendarAlt,
  FaComment,
} from 'react-icons/fa';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { signup, clearSuccess, clearError } from '../../redux/slice/authSlice';

const SignupForm = ({ role }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    password: '',
    password_confirmation: '',
    username: '',
    address: '',
    // Fields specific to Riders
    within_dunkwa_price: '',
    outside_dunkwa_price: '',
    ghana_card_number: '',
    vehicle_details: '',
    dob: '',
    drivers_license_number: '',
    vehicle_registration_number: '',
    rider_status: '',
    // Fields specific to Vendors
    operation_time: '',
    closing_time: '',
    digital_address: '',
    vendor_description: '',
    categories: [],
    // Mobile Money fields (for both Riders and Vendors)
    mobile_money_number: '',
    mobile_money_provider: '',
    mobile_money_name: '',
    // File fields
    avatar: null,
    front_drivers_license: null,
    back_drivers_license: null,
    front_ghana_card: null,
    back_ghana_card: null,
    moto_pic: null,
    vehicle_registration_pic: null,
    health_certificate_pic: null,
    food_certificate_pic: null,
    operation_license_pic: null,
  });

  const totalSteps = role === 'customer' ? 2 : 4;

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error, success } = useSelector((state) => state.auth);

  const availableCategories = ['Desserts', 'Drinks (Soft & Local)', 'Snacks & Pastries', 'Rice Dishes', 'Noodles & Pasta', 'International Cuisine', 'Others'];

  const handleChange = (e) => {
    const {
      name, value, files, type,
    } = e.target;
    if (name === 'categories' && type === 'checkbox') {
      const category = value;
      const isChecked = e.target.checked;
      setFormData((prevData) => {
        const newCategories = isChecked
          ? [...prevData.categories, category]
          : prevData.categories.filter((cat) => cat !== category);
        return { ...prevData, categories: newCategories };
      });
    } else if (files) {
      setFormData((prevData) => ({ ...prevData, [name]: files[0] }));
    } else {
      setFormData((prevData) => ({ ...prevData, [name]: value }));
    }
  };

  // Function to validate required fields for each step
  const validateStep = (step) => {
    const requiredFields = [];

    if (step === 1) {
      // Step 1: Basic information
      if (!formData.name.trim()) requiredFields.push('Name');
      if (!formData.phone.trim()) requiredFields.push('Phone');
      if (!formData.email.trim()) requiredFields.push('Email');
      if (!formData.password.trim()) requiredFields.push('Password');
      if (!formData.password_confirmation.trim()) requiredFields.push('Confirm Password');
      if (!formData.username.trim()) requiredFields.push('Username');
    }

    if (step === 2) {
      // Step 2: Role-specific information
      if (role === 'rider') {
        if (!formData.within_dunkwa_price) requiredFields.push('Within Dunkwa Delivery Price');
        if (!formData.ghana_card_number.trim()) requiredFields.push('Ghana Card Number');
        if (!formData.drivers_license_number.trim()) requiredFields.push('Driver\'s License Number');
        if (!formData.vehicle_registration_number.trim()) requiredFields.push('Vehicle Registration Number');
        if (!formData.dob) requiredFields.push('Date of Birth');
        if (!formData.mobile_money_number.trim()) requiredFields.push('Mobile Money Number');
        if (!formData.mobile_money_provider) requiredFields.push('Mobile Money Provider');
        if (!formData.mobile_money_name.trim()) requiredFields.push('Mobile Money Name');
      }

      if (role === 'vendor') {
        if (!formData.operation_time) requiredFields.push('Operation Time');
        if (!formData.closing_time) requiredFields.push('Closing Time');
        if (!formData.ghana_card_number.trim()) requiredFields.push('Ghana Card Number');
        if (!formData.vendor_description.trim()) requiredFields.push('Business Description');
        if (!formData.mobile_money_number.trim()) requiredFields.push('Mobile Money Number');
        if (!formData.mobile_money_provider) requiredFields.push('Mobile Money Provider');
        if (!formData.mobile_money_name.trim()) requiredFields.push('Mobile Money Name');
      }
    }

    if (step === 3) {
      // Step 3: Documents and files
      if (role === 'rider') {
        if (!formData.vehicle_details.trim()) requiredFields.push('Vehicle Details');
        if (!formData.avatar) requiredFields.push('Profile Picture');
        if (!formData.front_drivers_license) requiredFields.push('Front Driver\'s License');
        if (!formData.back_drivers_license) requiredFields.push('Back Driver\'s License');
      }

      if (role === 'vendor') {
        if (!formData.avatar) requiredFields.push('Logo');
        if (!formData.front_ghana_card) requiredFields.push('Front Ghana Card');
        if (!formData.back_ghana_card) requiredFields.push('Back Ghana Card');
        if (!formData.health_certificate_pic) requiredFields.push('Health Certificate');
        if (formData.categories.length === 0) requiredFields.push('At least one category');
      }
    }

    if (step === 4) {
      // Step 4: Final documents
      if (role === 'rider') {
        if (!formData.front_ghana_card) requiredFields.push('Front Ghana Card');
        if (!formData.back_ghana_card) requiredFields.push('Back Ghana Card');
        if (!formData.moto_pic) requiredFields.push('Motorcycle Picture');
        if (!formData.vehicle_registration_pic) requiredFields.push('Vehicle Registration Picture');
      }

      if (role === 'vendor') {
        if (!formData.food_certificate_pic) requiredFields.push('Food Certificate');
        if (!formData.operation_license_pic) requiredFields.push('Operation License');
      }
    }

    return requiredFields;
  };

  const handleNext = () => {
    // Check password match for step 1
    if (currentStep === 1 && formData.password !== formData.password_confirmation) {
      setPasswordError('Passwords do not match');
      return;
    }
    setPasswordError('');

    // Validate required fields for current step
    const missingFields = validateStep(currentStep);
    if (missingFields.length > 0) {
      toast.error(`Please fill in the following required fields: ${missingFields.join(', ')}`);
      return;
    }

    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  useEffect(() => {
    if (success) {
      toast.success('Signup successful! Please log in.');
      dispatch(clearSuccess());
      navigate('/');
    }
  }, [success, dispatch, navigate]);

  // clear inline errors if user starts typing again
  useEffect(() => () => {
    // if component unmounts or role changes, clear errors too
    dispatch(clearError());
  }, [dispatch]);

  const handleSubmit = () => {
    // Validate all required fields before submission
    const allMissingFields = [];

    // Validate all steps
    for (let step = 1; step <= totalSteps; step++) {
      const missingFields = validateStep(step);
      allMissingFields.push(...missingFields);
    }

    if (allMissingFields.length > 0) {
      toast.error(`Please fill in the following required fields: ${allMissingFields.join(', ')}`);
      return;
    }

    // Additional specific validations
    if (role === 'vendor' && formData.categories.length === 0) {
      toast.error('Please select at least one category.');
      return;
    }

    // Validate mobile money fields for vendors and riders
    if ((role === 'vendor' || role === 'rider')
        && (!formData.mobile_money_number
        || !formData.mobile_money_provider
        || !formData.mobile_money_name)) {
      toast.error('Please fill in all mobile money details for payouts.');
      return;
    }

    const formPayload = new FormData();
    Object.keys(formData).forEach((key) => {
      if (key === 'categories') {
        if (role === 'vendor') {
          formData.categories.forEach((category) => {
            formPayload.append(`${role}[category_names][]`, category);
          });
        }
      } else if (formData[key]) {
        formPayload.append(`${role}[${key}]`, formData[key]);
      }
    });

    dispatch(signup({ role, userData: formPayload }));
  };

  const handleTogglePassword = () => {
    setShowPassword((prevState) => !prevState);
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <>
            {/* Basic Information */}
            <div className="form-field">
              <label htmlFor="name" className="form-label">
                {role === 'customer' ? 'Full Name' : 'Brand Name'}
                <span className="required">*</span>
                <div className="input-container">
                  <FaUser className="input-icon" />
                  <input
                    id="name"
                    type="text"
                    name="name"
                    placeholder={role === 'customer' ? 'Enter your full name' : 'Enter your brand name'}
                    className="form-input"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>
              </label>
            </div>

            <div className="form-field">
              <label htmlFor="username" className="form-label">
                Username
                <span className="required">*</span>
                <div className="input-container">
                  <FaUserTag className="input-icon" />
                  <input
                    id="username"
                    type="text"
                    name="username"
                    placeholder="Choose a unique username"
                    className="form-input"
                    value={formData.username}
                    onChange={handleChange}
                    required
                  />
                </div>
              </label>
            </div>

            <div className="form-field">
              <label htmlFor="phone" className="form-label">
                Phone Number
                <span className="required">*</span>
                <div className="input-container">
                  <FaPhone className="input-icon" />
                  <input
                    id="phone"
                    type="text"
                    name="phone"
                    placeholder="Enter your phone number"
                    className="form-input"
                    value={formData.phone}
                    onChange={handleChange}
                    required
                  />
                </div>
              </label>
            </div>

            <div className="form-field">
              <label htmlFor="email" className="form-label">
                Email Address
                <span className="required">*</span>
                <div className="input-container">
                  <FaEnvelope className="input-icon" />
                  <input
                    id="email"
                    type="email"
                    name="email"
                    placeholder="Enter your email address"
                    className="form-input"
                    value={formData.email}
                    onChange={handleChange}
                    required
                  />
                </div>
              </label>
            </div>

            <div className="form-field">
              <label htmlFor="password" className="form-label">
                Password
                <span className="required">*</span>
                <div className="input-container">
                  <FaLock className="input-icon" />
                  <input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    placeholder="Create a strong password"
                    className="form-input"
                    value={formData.password}
                    onChange={handleChange}
                    required
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={handleTogglePassword}
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                  >
                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
              </label>
            </div>

            <div className="form-field">
              <label htmlFor="password_confirmation" className="form-label">
                Confirm Password
                <span className="required">*</span>
                <div className="input-container">
                  <FaLock className="input-icon" />
                  <input
                    id="password_confirmation"
                    type={showPassword ? 'text' : 'password'}
                    name="password_confirmation"
                    placeholder="Confirm your password"
                    className="form-input"
                    value={formData.password_confirmation}
                    onChange={handleChange}
                    required
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={handleTogglePassword}
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                  >
                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
                {passwordError && (
                <p className="error-message">
                  <FaEyeSlash style={{ marginRight: '0.5rem' }} />
                  {passwordError}
                </p>
                )}
              </label>
            </div>
          </>
        );
      case 2:
        return (
          <>
            {/* Address field - only for vendors and riders, optional */}
            {role !== 'customer' && (
              <div className="form-field">
                <label htmlFor="address" className="form-label">
                  Address (Optional)
                  <div className="input-container">
                    <FaMapMarkerAlt className="input-icon" />
                    <input
                      id="address"
                      type="text"
                      name="address"
                      placeholder="Enter your full address (optional)"
                      className="form-input"
                      value={formData.address}
                      onChange={handleChange}
                    />
                  </div>
                </label>
              </div>
            )}

            {role === 'rider' && (
            <>
              <div className="rider-note">
                <p>
                  <strong>Note:</strong>
                  {' '}
                  We add service fees to deliveries:
                </p>
                <ul>
                  <li>₵2 for deliveries within Dunkwa</li>
                  <li>₵4 for deliveries outside Dunkwa</li>
                </ul>
                <p>Example: If you set ₵5, customers will see ₵7.</p>
              </div>

              <div className="form-field">
                <label htmlFor="within_dunkwa_price" className="form-label">
                  Within Dunkwa Delivery Price
                  <span className="required">*</span>
                  <div className="input-container">
                    <FaMoneyBillWave className="input-icon" />
                    <input
                      id="within_dunkwa_price"
                      type="number"
                      name="within_dunkwa_price"
                      placeholder="Enter price for local deliveries"
                      className="form-input"
                      value={formData.within_dunkwa_price}
                      onChange={handleChange}
                      required
                      min="0"
                      step="0.01"
                    />
                  </div>
                </label>
              </div>

              <div className="form-field">
                <label htmlFor="outside_dunkwa_price" className="form-label">
                  Outside Dunkwa Delivery Price.
                  {' '}
                  <div className="skip">Please skip if you don’t deliver outside Dunkwa</div>
                </label>
                <div className="input-container">
                  <FaMoneyBillWave className="input-icon" />
                  <input
                    id="outside_dunkwa_price"
                    type="number"
                    name="outside_dunkwa_price"
                    placeholder="Outside Dunkwa Price (optional)"
                    className="form-input"
                    value={formData.outside_dunkwa_price}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>

              <div className="form-field">
                <label htmlFor="ghana_card_number" className="form-label">
                  Ghana Card Number
                  <span className="required">*</span>
                  <div className="input-container">
                    <FaIdCard className="input-icon" />
                    <input
                      id="ghana_card_number"
                      type="text"
                      name="ghana_card_number"
                      placeholder="Enter your Ghana Card number"
                      className="form-input"
                      value={formData.ghana_card_number}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </label>
              </div>

              <div className="form-field">
                <label htmlFor="drivers_license_number" className="form-label">
                  Driver&apos;s License Number
                  {' '}
                  <span className="required">*</span>
                  <div className="input-container">
                    <FaIdCard className="input-icon" />
                    <input
                      id="drivers_license_number"
                      type="text"
                      name="drivers_license_number"
                      placeholder="Enter your driver's license number"
                      className="form-input"
                      value={formData.drivers_license_number}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </label>
              </div>

              <div className="form-field">
                <label htmlFor="vehicle_registration_number" className="form-label">
                  Moto Registration Number
                  {' '}
                  <span className="required">*</span>
                  <div className="input-container">
                    <FaMotorcycle className="input-icon" />
                    <input
                      id="vehicle_registration_number"
                      type="text"
                      name="vehicle_registration_number"
                      placeholder="Enter your moto registration number"
                      className="form-input"
                      value={formData.vehicle_registration_number}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </label>
              </div>

              <div className="form-field">
                <label htmlFor="dob" className="form-label">
                  Date of Birth
                  {' '}
                  <span className="required">*</span>
                  <div className="input-container">
                    <FaCalendarAlt className="input-icon" />
                    <input
                      id="dob"
                      type="date"
                      name="dob"
                      className="form-input"
                      value={formData.dob}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </label>
              </div>

              <div className="mobile-money-section">
                <h4>Mobile Money Details for Payouts</h4>
                <p>These details will be used to send you payments when orders are completed.</p>

                <div className="form-field">
                  <label htmlFor="mobile_money_number" className="form-label">
                    Mobile Money Number
                    <span className="required">*</span>
                    <div className="input-container">
                      <FaPhone className="input-icon" />
                      <input
                        id="mobile_money_number"
                        type="text"
                        name="mobile_money_number"
                        placeholder="Enter your mobile money number"
                        className="form-input"
                        value={formData.mobile_money_number}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </label>
                </div>

                <div className="form-field">
                  <label htmlFor="mobile_money_provider" className="form-label">
                    Mobile Money Provider
                    <span className="required">*</span>
                    <div className="input-container">
                      <FaMoneyBillWave className="input-icon" />
                      <select
                        id="mobile_money_provider"
                        name="mobile_money_provider"
                        className="form-input"
                        value={formData.mobile_money_provider}
                        onChange={handleChange}
                        required
                      >
                        <option value="">Select Provider</option>
                        <option value="mtn">MTN Mobile Money</option>
                        <option value="vodafone">Vodafone Cash</option>
                        <option value="airteltigo">AirtelTigo Money</option>
                      </select>
                    </div>
                  </label>
                </div>

                <div className="form-field">
                  <label htmlFor="mobile_money_name" className="form-label">
                    Name on Mobile Money Account
                    <span className="required">*</span>
                    <div className="input-container">
                      <FaUser className="input-icon" />
                      <input
                        id="mobile_money_name"
                        type="text"
                        name="mobile_money_name"
                        placeholder="Enter the name on your mobile money account"
                        className="form-input"
                        value={formData.mobile_money_name}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </label>
                </div>
              </div>
            </>
            )}

            {role === 'vendor' && (
            <>
              <label htmlFor="operation_time" className="form-label">
                Operation Time
                {' '}
                <span className="required">*</span>
              </label>
              <input
                id="operation_time"
                type="time"
                name="operation_time"
                placeholder="Operation Time"
                className="form-input"
                value={formData.operation_time}
                onChange={handleChange}
                required
              />

              <label htmlFor="closing_time" className="form-label">
                Closing Time
                {' '}
                <span className="required">*</span>
              </label>
              <input
                id="closing_time"
                type="time"
                name="closing_time"
                placeholder="Closing Time"
                className="form-input"
                value={formData.closing_time}
                onChange={handleChange}
                required
              />

              <label htmlFor="digital_address" className="form-label">
                Digital Address (Optional)
              </label>
              <input
                id="digital_address"
                type="text"
                name="digital_address"
                placeholder="Digital Address (optional)"
                className="form-input"
                value={formData.digital_address}
                onChange={handleChange}
              />

              <div className="form-field">
                <label htmlFor="ghana_card_number" className="form-label">
                  Ghana Card Number
                  {' '}
                  <span className="required">*</span>
                  <div className="input-container">
                    <FaIdCard className="input-icon" />
                    <input
                      id="ghana_card_number"
                      type="text"
                      name="ghana_card_number"
                      placeholder="Enter your Ghana Card number"
                      className="form-input"
                      value={formData.ghana_card_number}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </label>
              </div>

              <div className="form-field">
                <label htmlFor="vendor_description" className="form-label">
                  Describe Your Business
                  <span className="required">*</span>
                  <div className="input-container">
                    <FaComment size={16} className="input-icon" />
                    <textarea
                      id="vendor_description"
                      name="vendor_description"
                      placeholder="Tell customers about your food, style, and specials"
                      className="form-input"
                      value={formData.vendor_description}
                      onChange={handleChange}
                      required
                      rows={4}
                    />
                  </div>
                </label>
              </div>
              <div className="mobile-money-section">
                <h4>Mobile Money Details for Payouts</h4>
                <p>These details will be used to send you payments when orders are completed.</p>
                <label htmlFor="mobile_money_number" className="form-label">
                  Mobile Money Number
                  {' '}
                  <span className="required">*</span>
                </label>
                <input
                  id="mobile_money_number"
                  type="text"
                  name="mobile_money_number"
                  placeholder="Mobile Money Number"
                  className="form-input"
                  value={formData.mobile_money_number}
                  onChange={handleChange}
                  required
                />

                <label htmlFor="mobile_money_provider" className="form-label">
                  Mobile Money Provider
                  {' '}
                  <span className="required">*</span>
                </label>
                <select
                  id="mobile_money_provider"
                  name="mobile_money_provider"
                  className="form-input"
                  value={formData.mobile_money_provider}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Provider</option>
                  <option value="mtn">MTN Mobile Money</option>
                  <option value="vodafone">Vodafone Cash</option>
                  <option value="airteltigo">AirtelTigo Money</option>
                </select>

                <label htmlFor="mobile_money_name" className="form-label">
                  Name on Mobile Money Account
                  {' '}
                  <span className="required">*</span>
                </label>
                <input
                  id="mobile_money_name"
                  type="text"
                  name="mobile_money_name"
                  placeholder="Name on Mobile Money Account"
                  className="form-input"
                  value={formData.mobile_money_name}
                  onChange={handleChange}
                  required
                />
              </div>
            </>
            )}

            {/* Customer completion message */}
            {role === 'customer' && (
              <div className="completion-message">
                <h4>🎉 Almost Done!</h4>
                <p>You're ready to start ordering delicious food from local vendors. Click Submit to create your account.</p>
              </div>
            )}
          </>
        );
      case 3:
        return (
          <>
            {role === 'rider' && (
            <>
              <label htmlFor="vehicle_details" className="form-label">
                Moto Details
                {' '}
                <span className="required">*</span>
              </label>
              <input
                id="vehicle_details"
                type="text"
                name="vehicle_details"
                placeholder="Moto Details"
                className="form-input"
                value={formData.vehicle_details}
                onChange={handleChange}
                required
              />

              <label htmlFor="avatar" className="form-label">
                Profile Picture
                {' '}
                <span className="required">*</span>
              </label>
              <input
                id="avatar"
                type="file"
                name="avatar"
                className="form-input"
                onChange={handleChange}
                required
              />

              <label htmlFor="front_drivers_license" className="form-label">
                Front Driver&apos;s License
                {' '}
                <span className="required">*</span>
              </label>
              <input
                id="front_drivers_license"
                type="file"
                name="front_drivers_license"
                className="form-input"
                onChange={handleChange}
                required
              />

              <label htmlFor="back_drivers_license" className="form-label">
                Back Driver&apos;s License
                {' '}
                <span className="required">*</span>
              </label>
              <input
                id="back_drivers_license"
                type="file"
                name="back_drivers_license"
                className="form-input"
                onChange={handleChange}
                required
              />
            </>
            )}
            {role === 'vendor' && (
            <>
              <div className="form-field">
                <label htmlFor="avatar" className="form-label">
                  Upload Your Brand Logo
                  <span className="required">*</span>
                  <div className="input-container">
                    <input
                      id="avatar"
                      type="file"
                      name="avatar"
                      accept="image/*"
                      className="form-input"
                      onChange={handleChange}
                      required
                    />
                  </div>
                </label>
              </div>
              <div className="form-field">
                <label htmlFor="front_ghana_card" className="form-label">
                  Front Ghana Card
                  <span className="required">*</span>
                  <div className="input-container">
                    <input
                      id="front_ghana_card"
                      type="file"
                      name="front_ghana_card"
                      className="form-input"
                      onChange={handleChange}
                      accept="image/*"
                      required
                    />
                  </div>
                </label>
              </div>

              <div className="form-field">
                <label htmlFor="back_ghana_card" className="form-label">
                  Back Ghana Card
                  <span className="required">*</span>
                  <div className="input-container">
                    <input
                      id="back_ghana_card"
                      type="file"
                      name="back_ghana_card"
                      className="form-input"
                      onChange={handleChange}
                      accept="image/*"
                      required
                    />
                  </div>
                </label>
              </div>

              <div className="form-field">
                <label htmlFor="health_certificate_pic" className="form-label">
                  Health Certificate
                  <span className="required">*</span>
                  <div className="input-container">
                    <input
                      id="health_certificate_pic"
                      type="file"
                      name="health_certificate_pic"
                      className="form-input"
                      onChange={handleChange}
                      accept="image/*"
                      required
                    />
                  </div>
                </label>
              </div>
              <div>
                <label htmlFor="categories" className="form-label">
                  Categories (Select all that apply)
                  {' '}
                  <span className="required">*</span>
                </label>
                <div className="main-checkbox-container">
                  {availableCategories.map((category) => (
                    <div key={category} className="checkbox-container">
                      <input
                        type="checkbox"
                        id={category}
                        name="categories"
                        value={category}
                        checked={formData.categories.includes(category)}
                        onChange={handleChange}
                      />
                      <label htmlFor={category}>{category}</label>
                    </div>
                  ))}
                </div>
              </div>
            </>
            )}
          </>
        );
      case 4:
        return (
          <>
            {role === 'rider' && (
              <>
                <div className="form-field">
                  <label htmlFor="front_ghana_card_rider" className="form-label">
                    Front Ghana Card
                    <span className="required">*</span>
                    <div className="input-container">
                      <input
                        id="front_ghana_card_rider"
                        type="file"
                        name="front_ghana_card"
                        className="form-input"
                        onChange={handleChange}
                        accept="image/*"
                        required
                      />
                    </div>
                  </label>
                </div>

                <div className="form-field">
                  <label htmlFor="back_ghana_card_rider" className="form-label">
                    Back Ghana Card
                    <span className="required">*</span>
                    <div className="input-container">
                      <input
                        id="back_ghana_card_rider"
                        type="file"
                        name="back_ghana_card"
                        className="form-input"
                        onChange={handleChange}
                        accept="image/*"
                        required
                      />
                    </div>
                  </label>
                </div>

                <div className="form-field">
                  <label htmlFor="moto_pic" className="form-label">
                    Motorcycle Picture
                    <span className="required">*</span>
                    <div className="input-container">
                      <input
                        id="moto_pic"
                        type="file"
                        name="moto_pic"
                        className="form-input"
                        onChange={handleChange}
                        accept="image/*"
                        required
                      />
                    </div>
                  </label>
                </div>

                <div className="form-field">
                  <label htmlFor="vehicle_registration_pic" className="form-label">
                    Moto Registration Picture
                    <span className="required">*</span>
                    <div className="input-container">
                      <input
                        id="vehicle_registration_pic"
                        type="file"
                        name="vehicle_registration_pic"
                        className="form-input"
                        onChange={handleChange}
                        accept="image/*"
                        required
                      />
                    </div>
                  </label>
                </div>
              </>
            )}
            {role === 'vendor' && (
              <>
                <div className="form-field">
                  <label htmlFor="food_certificate_pic" className="form-label">
                    Food Certificate
                    <span className="required">*</span>
                    <div className="input-container">
                      <input
                        id="food_certificate_pic"
                        type="file"
                        name="food_certificate_pic"
                        className="form-input"
                        onChange={handleChange}
                        accept="image/*"
                        required
                      />
                    </div>
                  </label>
                </div>

                <div className="form-field">
                  <label htmlFor="operation_license_pic" className="form-label">
                    Operation License
                    <span className="required">*</span>
                    <div className="input-container">
                      <input
                        id="operation_license_pic"
                        type="file"
                        name="operation_license_pic"
                        className="form-input"
                        onChange={handleChange}
                        accept="image/*"
                        required
                      />
                    </div>
                  </label>
                </div>
              </>
            )}
          </>
        );
      default:
        return null;
    }
  };

  // Generate step indicators based on total steps
  const renderStepIndicators = () => {
    const indicators = [];
    // Using Array.from to avoid the linter warning about the ++ operator
    Array.from({ length: totalSteps }, (_, index) => {
      const stepNumber = index + 1;
      indicators.push(
        <div
          key={stepNumber}
          className={`step-indicator ${currentStep >= stepNumber ? 'active' : ''}`}
          aria-label={`Step ${stepNumber} of ${totalSteps}`}
        />,
      );
      return null; // Just to satisfy the map function
    });
    return indicators;
  };

  return (
    <div className="auth-page">
      <div className="auth-form">
        <div className="auth-form-header">
          <h2>
            {role === 'customer' && <FaUser style={{ marginRight: '0.5rem' }} />}
            {role === 'vendor' && <FaUtensils style={{ marginRight: '0.5rem' }} />}
            {role === 'rider' && <FaMotorcycle style={{ marginRight: '0.5rem' }} />}
            Create Your EaseFood Account
          </h2>
          <div className="role-badge">
            {role === 'customer' ? 'Customer Account' : ''}
            {role === 'vendor' ? 'Food Vendor Account' : ''}
            {role === 'rider' ? 'Delivery Rider Account' : ''}
          </div>
          <p className="message">
            {role === 'customer' && 'Order delicious meals from local vendors and have them delivered to your doorstep'}
            {role === 'vendor' && 'Sell your delicious food and grow your business with our platform'}
            {role === 'rider' && 'Join our delivery team and earn money delivering food to customers'}
          </p>
        </div>

        <div className="auth-form-content">
          <div className="form-steps">
            {renderStepIndicators()}
          </div>

          {error && (
            <div className="error-message">
              <FaEyeSlash style={{ marginRight: '0.5rem' }} />
              {error}
            </div>
          )}

          {renderStep()}

          <div className="form-navigation">
            {currentStep > 1 && (
              <button
                type="button"
                className="nav-button back-button"
                onClick={handleBack}
                aria-label="Go back to previous step"
              >
                Back
              </button>
            )}
            {currentStep < totalSteps ? (
              <button
                type="button"
                className="nav-button next-button"
                onClick={handleNext}
                aria-label="Continue to next step"
              >
                Next
              </button>
            ) : (
              <button
                type="button"
                className="nav-button submit-button"
                onClick={handleSubmit}
                disabled={loading}
                aria-label="Submit signup form"
              >
                {loading ? 'Signing up...' : 'Submit'}
              </button>
            )}
          </div>
        </div>

        <div className="auth-form-footer">
          <p className="account-link">
            Already have an account?
            {' '}
            <a href="/login">Login</a>
          </p>
        </div>
      </div>
    </div>
  );
};

SignupForm.propTypes = {
  role: PropTypes.string.isRequired,
};

export default SignupForm;
