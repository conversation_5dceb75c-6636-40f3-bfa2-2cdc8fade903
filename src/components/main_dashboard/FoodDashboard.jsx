import { useState, useMemo, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Carousel } from 'react-responsive-carousel';
import 'react-responsive-carousel/lib/styles/carousel.min.css';
import { toast } from 'react-toastify';
import { useSelector, useDispatch } from 'react-redux';
import {
  ShoppingCart,
  Tag,
  ChevronDown,
  ChevronUp,
} from 'lucide-react';
import { addCartItem } from '../../redux/slice/cartsSlice';
import { hasMultiplePrices, getPriceOptions } from '../../utils/priceUtils';
import fd from '../../assets/fd.jpg';
import food from '../../assets/food.png';
import foodSecond from '../../assets/foood.png';

const FoodDashboard = () => {
  const { vendorId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [expandedDescriptions, setExpandedDescriptions] = useState({});
  // State for selected price indices for each food item
  const [selectedPriceIndices, setSelectedPriceIndices] = useState({});
  const vendors = useSelector((state) => state.vendors.vendors);
  const { user } = useSelector((state) => state.auth);

  // Safely get customerId, will be null if user is not authenticated
  const customerId = user ? user.id : null;

  // Redirect unauthenticated users to login page if needed
  useEffect(() => {
    // Uncomment the following code if you want to redirect unauthenticated users
    // if (!user) {
    //   toast.error('Please log in to view vendor menus');
    //   navigate('/login');
    // }
  }, [user, navigate]);

  // Memoize vendor lookup to avoid recalculating on every render
  const vendor = useMemo(() => vendors.find((v) => v.id === parseInt(vendorId, 10)),
    [vendors, vendorId]);

  // Memoize vendor foods to avoid recalculating on every render
  const vendorFoods = useMemo(() => (vendor && Array.isArray(vendor.foods) ? vendor
    .foods : []), [vendor]);
  // Function to handle adding food to the cart
  const handleAddToCart = (foodId) => {
    if (!user) {
      toast.error('Please log in to add items to cart.');
      // Optionally redirect to login page
      navigate('/login');
      return;
    }

    const quantity = 1;
    const selectedPriceIndex = selectedPriceIndices[foodId] || 0;

    // Pass the selected price index to the cart
    dispatch(addCartItem({
      customerId, foodId, quantity, priceIndex: selectedPriceIndex,
    }))
      .then(() => {
        toast.success('Food added to cart successfully!');
      })
      .catch((error) => {
        // Log error in non-production environments
        if (process.env.NODE_ENV !== 'production') {
          // eslint-disable-next-line no-console
          console.error('Cart error:', error);
        }
        toast.error('Failed to add food to cart.');
      });
  };

  // Direct add to cart function
  const handleAddToCartDirectly = (foodId) => {
    handleAddToCart(foodId);
  };

  // Toggle description expand/collapse
  const toggleDescription = (foodId) => {
    setExpandedDescriptions((prev) => ({
      ...prev,
      [foodId]: !prev[foodId],
    }));
  };

  return (
    <section className="food-dashboard-container">
      {/* Hero Banner */}
      <div className="food-banner">
        <Carousel
          showThumbs={false}
          showStatus={false}
          showArrows
          infiniteLoop
          autoPlay
          interval={5000}
        >
          <div className="food-hero-banner">
            <img src={food} alt="Food banner" />
          </div>
          <div className="food-hero-banner">
            <img src={foodSecond} alt="Food banner" />
          </div>
        </Carousel>

        {/* Vendor name displayed below carousel instead of overlay */}
      </div>

      {/* Menu Section */}
      <div className="food-menu-section">
        <div className="vendor-title-container">
          <h1 className="vendor-title">{vendor ? vendor.name : 'Vendor'}</h1>
          <p className="vendor-subtitle">Explore our delicious menu items</p>
        </div>

        <div className="section-header">
          <h2>Menu</h2>
        </div>

        {vendorFoods.length > 0 ? (
          <div className="food-grid">
            {vendorFoods.map((food) => (
              <div key={food.id} className="food-card">
                <div className="food-image-container">
                  <img
                    className="food-image"
                    src={food.food_image_url || fd}
                    alt={food.name}
                  />
                </div>
                <div className="food-content">
                  <h3 className="food-name">{food.name}</h3>
                  <div className="food-description-container">
                    <p className={`food-description ${expandedDescriptions[food.id] ? 'expanded' : 'collapsed'}`}>
                      {food.description}
                    </p>
                    {food.description && food.description.length > 60 && (
                      <button
                        type="button"
                        className="description-toggle"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleDescription(food.id);
                        }}
                      >
                        {expandedDescriptions[food.id] ? (
                          <>
                            See Less
                            <ChevronUp size={14} />
                          </>
                        ) : (
                          <>
                            See More
                            <ChevronDown size={14} />
                          </>
                        )}
                      </button>
                    )}
                  </div>
                  <div className="food-price-row">
                    {hasMultiplePrices(food) ? (
                      <div className="multiple-prices-display">
                        <div className="price-range">
                          <Tag size={16} className="price-tag-icon" />
                          <span>
                            GH₵
                            {food.min_price}
                            {' '}
                            - GH₵
                            {food.max_price}
                          </span>
                        </div>
                        <select
                          className="price-select"
                          value={selectedPriceIndices[food.id] || 0}
                          onChange={(e) => {
                            const newIndex = parseInt(e.target.value || 0, 10);
                            setSelectedPriceIndices((prev) => ({
                              ...prev,
                              [food.id]: newIndex,
                            }));
                          }}
                        >
                          {getPriceOptions(food).map((option) => (
                            <option key={option.index} value={option.index}>
                              {option.displayPrice}
                            </option>
                          ))}
                        </select>
                      </div>
                    ) : (
                      <p className="food-price">
                        <Tag size={16} className="price-tag-icon" />
                        <span>
                          GH₵
                          {food.price}
                        </span>
                      </p>
                    )}
                  </div>
                  <button
                    type="button"
                    className="add-to-cart-btn"
                    onClick={() => handleAddToCartDirectly(food.id)}
                  >
                    <ShoppingCart size={16} />
                    Add to Cart
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="no-foods-message">
            <p>No menu items available for this vendor.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default FoodDashboard;
