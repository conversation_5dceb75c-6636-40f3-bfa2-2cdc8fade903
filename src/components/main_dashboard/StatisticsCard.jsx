import PropTypes from 'prop-types';

const StatisticsCard = ({
  title, value, icon, color = '#f8b400', period = 'Today', change = null, changeType = 'increase',
}) => (
  <div className="statistics-card">
    <div className="statistics-card-header">
      <div className="statistics-card-icon" style={{ backgroundColor: `${color}20` }}>
        {icon}
      </div>
      <div className="statistics-card-title">{title}</div>
    </div>
    <div className="statistics-card-value">{value}</div>
    <div className="statistics-card-footer">
      <div className="statistics-card-period">{period}</div>
      {change && (
        <div className={`statistics-card-change ${changeType}`}>
          {changeType === 'increase' ? '↑' : '↓'}
          {' '}
          {change}
        </div>
      )}
    </div>
  </div>
);

StatisticsCard.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  icon: PropTypes.node.isRequired,
  color: PropTypes.string.isRequired,
  period: PropTypes.string.isRequired,
  change: PropTypes.string.isRequired,
  changeType: PropTypes.oneOf(['increase', 'decrease']).isRequired,
};

export default StatisticsCard;
