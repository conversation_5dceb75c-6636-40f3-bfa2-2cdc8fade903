.wave-banner {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
  margin-bottom: 1rem;
  margin-top: 4rem;
}

.wave-banner-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  filter: brightness(0.7);
  transition: all 1s ease;
  z-index: 1;
}

.wave-banner-content {
  position: relative;
  z-index: 3;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #fff;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  padding: 0 1rem;
}

.wave-banner h2 {
  font-size: 1.75rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
}

.wave-banner h2 .banner-icon {
  margin-right: 0.75rem;
  font-size: 2.25rem;
}

.wave-banner p {
  font-size: 1rem;
  max-width: 600px;
  margin-bottom: 1rem;
  line-height: 1.6;
  font-weight: 900;
  text-align: center;
}

.wave-banner .dynamic-text {
  font-weight: 700;
  display: inline-block;
  position: relative;
  margin: 0 0.25rem;
  font-size: 1.4rem;
  text-decoration: underline;
  text-underline-offset: 4px;
  text-decoration-thickness: 2px;
  transition: all 0.5s ease;
  animation: dropIn 0.5s ease-out forwards;
  padding: 0 0.5rem;
  border-radius: 4px;
}

@keyframes dropIn {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
  }

  70% {
    transform: translateY(5px) scale(1.05);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Different styles for dynamic text */
.wave-banner .dynamic-text-0 {
  color: #8a2be2; /* Purple */
  text-decoration-color: #8a2be2;
}

.wave-banner .dynamic-text-1 {
  color: #ff6b6b; /* Red */
  text-decoration-color: #ff6b6b;
}

.wave-banner .dynamic-text-2 {
  color: #4ecdc4; /* Teal */
  text-decoration-color: #4ecdc4;
}

.wave-banner .dynamic-text-3 {
  color: #7d5fff; /* Purple */
  text-decoration-color: #7d5fff;
}

.wave-banner .dynamic-text-4 {
  color: #ff9f43; /* Orange */
  text-decoration-color: #ff9f43;
}

.wave-banner .dynamic-text-5 {
  color: #2ecc71; /* Green */
  text-decoration-color: #2ecc71;
}

.wave-banner .wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="white" fill-opacity="1" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,202.7C672,203,768,181,864,181.3C960,181,1056,203,1152,208C1248,213,1344,203,1392,197.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-size: cover;
  background-repeat: no-repeat;
  z-index: 2;
}

/* Food icons decoration */
.food-icon {
  position: absolute;
  opacity: 0.6;
  z-index: 1;
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.3));
  color: rgba(255, 255, 255, 0.9);
  font-size: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.food-icon-1 {
  top: 10%;
  right: 0%;
  font-size: 1.5rem;
  transform: rotate(15deg);
}

.food-icon-2 {
  bottom: 4%;
  right: 10%;
  font-size: 1.5rem;
  transform: rotate(-10deg);
}

.food-icon-3 {
  top: 10%;
  right: 50%;
  font-size: 1.5rem;
  transform: rotate(5deg);
}

.food-icon-4 {
  top: 10%;
  right: 91%;
  font-size: 1.5rem;
  transform: rotate(-5deg);
}

.food-icon-5 {
  top: 73%;
  left: 3%;
  font-size: 1.5rem;
  transform: rotate(-8deg);
}

/* Vendor-specific styles */
.vendor-wave-banner {
  background: linear-gradient(135deg, #f90 0%, #f60 100%);
}

/* Rider-specific styles */
.rider-wave-banner {
  background: linear-gradient(135deg, #39f 0%, #06c 100%);
}

/* Customer-specific styles */
.customer-wave-banner {
  background: linear-gradient(135deg, #8a2be2 0%, #6a0dad 100%);
}
