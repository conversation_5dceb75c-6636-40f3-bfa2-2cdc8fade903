import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import './WaveBanner.css';
import {
  Utensils,
  Truck,
} from 'lucide-react';

const WaveBanner = ({
  type,
  title,
  subtitle,
  dynamicTexts = [],
}) => {
  const [currentTextIndex, setCurrentTextIndex] = useState(0);

  // Change the dynamic text every 3 seconds
  useEffect(() => {
    if (!dynamicTexts || dynamicTexts.length === 0) return;

    const interval = setInterval(() => {
      setCurrentTextIndex((prevIndex) => (prevIndex + 1) % dynamicTexts.length);
    }, 3000);

    // eslint-disable-next-line consistent-return
    return function cleanup() {
      clearInterval(interval);
    };
  }, [dynamicTexts]);

  // Get the appropriate icon based on the type
  const getBannerIcon = () => {
    if (type === 'vendor') {
      return <Utensils className="banner-icon" />;
    }
    return <Truck className="banner-icon" />;
  };

  return (
    <div className={`wave-banner ${type === 'vendor' ? 'vendor-wave-banner' : 'rider-wave-banner'}`}>
      {/* Food-related decorative icons - rendered directly in the DOM */}
      <div className="food-icon food-icon-1">🍕</div>
      <div className="food-icon food-icon-2">☕</div>
      <div className="food-icon food-icon-3">🍽️</div>
      <div className="food-icon food-icon-4">🛍️</div>
      <div className="food-icon food-icon-5">🍎</div>

      <div className="wave-banner-content">
        <h2>
          {getBannerIcon()}
          {title}
        </h2>
        <p>
          {subtitle.split('{dynamicText}')[0]}
          {dynamicTexts && dynamicTexts.length > 0 && (
            <span
              className={`dynamic-text dynamic-text-${currentTextIndex % 6}`}
              // Key to force re-render and restart animation
              key={dynamicTexts[currentTextIndex]}
            >
              {dynamicTexts[currentTextIndex]}
            </span>
          )}
          {subtitle.split('{dynamicText}')[1] || ''}
        </p>
      </div>
      <div className="wave" />
    </div>
  );
};

// Add prop validation
WaveBanner.propTypes = {
  type: PropTypes.oneOf(['vendor', 'rider']).isRequired,
  title: PropTypes.string.isRequired,
  subtitle: PropTypes.string.isRequired,
  dynamicTexts: PropTypes.arrayOf(PropTypes.string).isRequired,
};

export default WaveBanner;
