import {
  useEffect, useState, useCallback, useMemo,
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import axios from 'axios';
import { toast } from 'react-toastify';
import {
  Tag, User, Phone, MapPin, Clock, DollarSign, ChefHat,
  Menu, X, Package, Truck, Bike, CheckCircle, BarChart,
  XCircle, HelpCircle, Bell, RotateCcw,
} from 'lucide-react';
import useLocalStorage from '../../hooks/useLocalStorage';
import WaveBanner from './WaveBanner';
import { fetchOrders, updateOrderStatus } from '../../redux/slice/ordersSlice';
import { fetchVendors } from '../../redux/slice/vendersSlice';
import convertToLocalTime from '../helper-functions/convertToLocalTime';
import ApiUrl from '../helper-functions/ApiUrl';
import AutoRefresh from '../helper-functions/AutoRefresh';
import DashboardTutorial from '../helper-functions/DashboardTutorial';
import MobileNotificationBadge from '../notifications/MobileNotificationBadge';

import PickupNotificationsPanel from '../notifications/PickupNotificationsPanel';
import RiderStatisticsSection from './RiderStatisticsSection';
import '../notifications/PickupNotificationsPanel.css';

// Helper function to get vendor information from order data
// We'll keep this outside the component since it doesn't depend on component state
const getVendorInfoBase = (order, vendorsArray, field) => {
  // Priority 1: Check for direct vendor object (single vendor case)
  if (order.vendor && order.vendor[field]) {
    return order.vendor[field];
  }

  // Priority 2: Check for vendors array (multiple vendors case)
  if (order.vendors && order.vendors.length > 0) {
    // If we have multiple vendors, we need to determine which one to show
    // For now, we'll show the first vendor's information
    // This is appropriate for the rider's dashboard since they typically
    // pick up from one vendor at a time
    if (order.vendors[0] && order.vendors[0][field]) {
      return order.vendors[0][field];
    }
  }

  // Priority 3: Check for vendor information in food items
  // This is useful when the order contains foods from different vendors
  if (order.foods && order.foods.length > 0) {
    // Group foods by vendor_id to find the primary vendor
    const vendorCounts = {};
    order.foods.forEach((food) => {
      if (food.vendor_id) {
        vendorCounts[food.vendor_id] = (vendorCounts[food.vendor_id] || 0) + 1;
      }
    });

    // Find the vendor with the most food items
    let primaryVendorId = null;
    let maxCount = 0;

    Object.entries(vendorCounts).forEach(([vendorId, count]) => {
      if (count > maxCount) {
        maxCount = count;
        primaryVendorId = vendorId;
      }
    });

    // If we found a primary vendor, look for its details
    if (primaryVendorId) {
      // Check if this vendor is in the vendors array of the order
      if (order.vendors) {
        const primaryVendor = order.vendors.find(
          (v) => v.id.toString() === primaryVendorId.toString(),
        );
        if (primaryVendor && primaryVendor[field]) {
          return primaryVendor[field];
        }
      }

      // As a fallback, check the Redux store vendors
      if (vendorsArray) {
        const vendorFromStore = vendorsArray.find(
          (v) => v.id.toString() === primaryVendorId.toString(),
        );
        if (vendorFromStore && vendorFromStore[field]) {
          return vendorFromStore[field];
        }
      }
    }
  }

  // Priority 4: Check for direct vendor properties on the order (for backward compatibility)
  if (field === 'name' && order.vendor_name) {
    return order.vendor_name;
  }

  if (field === 'phone' && order.vendor_phone) {
    return order.vendor_phone;
  }

  if (field === 'address' && order.vendor_address) {
    return order.vendor_address;
  }

  // No vendor information found
  return null;
};

// Tutorial trigger button styles
const tutorialTriggerStyles = `
  .tutorial-trigger-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, #f8b400 0%, #e5a700 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(248, 180, 0, 0.3);
    z-index: 10;
  }

  .tutorial-trigger-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(248, 180, 0, 0.4);
  }

  .vendors-dashboard-header {
    position: relative;
  }

  @media (max-width: 768px) {
    .tutorial-trigger-btn {
      top: 0.5rem;
      right: 0.5rem;
      padding: 0.4rem 0.8rem;
      font-size: 0.8rem;
    }
  }
`;

// Add styles to document
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = tutorialTriggerStyles;
  document.head.appendChild(styleSheet);
}

const RidersDashboard = () => {
  const dispatch = useDispatch();
  const orders = useSelector((state) => state.orders.orders);
  const status = useSelector((state) => state.orders.status);
  const error = useSelector((state) => state.orders.error);
  const vendors = useSelector((state) => state.vendors.vendors);

  const { user } = useSelector((state) => state.auth);
  const riderId = user?.id;

  const [activeSection, setActiveSection] = useState('dashboard');
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [errorMessage, setErrorMessage] = useState('');
  const [payouts, setPayouts] = useState([]);
  const [payoutsLoading, setPayoutsLoading] = useState(false);
  const [confirmingOrderId, setConfirmingOrderId] = useState(null);
  const [showNotificationsPanel, setShowNotificationsPanel] = useState(true);
  // Set sidebar collapsed by default on mobile
  const [sidebarCollapsed, setSidebarCollapsed] = useState(window.innerWidth <= 768);
  // Initialize viewedOrderIds using custom hook
  const [viewedOrderIds, setViewedOrderIds] = useLocalStorage('viewedOrderIds', []);

  // State for draggable notification toggle button position
  const [buttonPosition, setButtonPosition] = useLocalStorage('notificationButtonPosition', { top: '0.625rem', right: '0.625rem' });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [longPressTimer, setLongPressTimer] = useState(null);
  const [isDragMode, setIsDragMode] = useState(false);
  const [expandedDescriptions, setExpandedDescriptions] = useState({});

  // Use custom hook for last reset date
  const [lastResetDate, setLastResetDate] = useLocalStorage('lastViewedOrdersReset', '');

  // Tutorial state
  const [showTutorial, setShowTutorial] = useLocalStorage('riderTutorialShown', false);

  // Tutorial steps for Riders Dashboard
  const tutorialSteps = [
    {
      title: 'Welcome to Your Rider Dashboard!',
      description: "Let's take a quick tour to help you navigate and use all the features effectively.",
      icon: <Bike size={48} />,
      details: 'This tutorial will show you how to manage orders, track deliveries, and access important features.',
    },
    {
      title: 'Navigation Sidebar',
      description: 'Use the sidebar to switch between different sections of your dashboard.',
      targetSelector: '.dashboard-sidebar',
      icon: <Menu size={48} />,
      details: "• Dashboard: View your statistics and overview\n• Ready for Pickup: Orders waiting for collection\n• Out for Delivery: Orders you're currently delivering\n• Delivered Orders: Completed deliveries\n• Not Received: Orders that weren't delivered\n• Received Orders: Orders confirmed as received by customers\n• Payouts: Your earnings and payment history",
    },
    {
      title: 'Notifications Panel',
      description: 'Stay updated with real-time notifications about new orders ready for pickup.',
      targetSelector: '.notifications-toggle',
      icon: <Bell size={48} />,
      details: '• Click to show/hide notifications\n• Drag to reposition (long press on mobile)\n• Badge shows number of unread notifications\n• Reset button clears notification history',
    },
    {
      title: 'Auto-Refresh Feature',
      description: 'Your dashboard automatically updates every 5 minutes to keep data fresh.',
      targetSelector: '.auto-refresh-container',
      icon: <RotateCcw size={48} />,
      details: '• Data refreshes automatically in the background\n• Click the button to refresh manually\n• Progress ring shows time until next refresh\n• No page reloads or interruptions',
    },
    {
      title: 'Order Management',
      description: 'View order details, customer information, and update delivery status.',
      targetSelector: '.vendors-orders-grid',
      icon: <Package size={48} />,
      details: '• View customer details and delivery address\n• See vendor information and pickup location\n• Update order status as you progress\n• Confirm delivery to complete orders',
    },
    {
      title: 'Status Updates',
      description: 'Keep customers informed by updating order status throughout the delivery process.',
      targetSelector: '.vendors-status-select',
      icon: <CheckCircle size={48} />,
      details: "• 'Out for Delivery': When you start delivery\n• 'Delivered': When order is successfully delivered\n• Status changes trigger notifications to customers\n• Confirm delivery to release payments",
    },
    {
      title: "You're All Set!",
      description: 'You now know how to navigate your rider dashboard effectively.',
      icon: <Bike size={48} />,
      details: '• Check notifications regularly for new orders\n• Update order status promptly\n• Use the auto-refresh to stay updated\n• Access payouts to track your earnings\n\nHappy delivering! 🚚',
    },
  ];

  // Fetch orders and vendors when the component mounts
  useEffect(() => {
    if (riderId) {
      // Check if we need to reset viewed orders (e.g., new day)
      const today = new Date().toDateString();

      if (lastResetDate !== today) {
        // Clear viewed orders at the start of a new day
        setViewedOrderIds([]);
        setLastResetDate(today);
      }
    }
  }, [riderId, lastResetDate, setViewedOrderIds, setLastResetDate]);

  useEffect(() => {
    if (riderId && status === 'idle') {
      dispatch(fetchOrders({ riderId }));
      dispatch(fetchVendors());
    }
  }, [status, riderId, dispatch]);

  // Handle responsive sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      // Only auto-collapse on mobile
      if (window.innerWidth <= 768) {
        setSidebarCollapsed(true);
      }
      // On desktop, we don't auto-expand anymore to allow user preference
      // This allows the sidebar to stay collapsed if the user wants it that way
    };

    // Set initial state
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle mouse move and mouse up events for dragging
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (isDragging && isDragMode) {
        // Calculate new position based on mouse position and initial offset
        const newRight = Math.max(0, window.innerWidth - e.clientX - dragOffset.x);
        const newTop = Math.max(0, e.clientY - dragOffset.y);

        // Convert to rem units
        const remRight = `${newRight / 16}rem`;
        const remTop = `${newTop / 16}rem`;

        // Update position
        setButtonPosition({ top: remTop, right: remRight });
      }
    };

    const handleMouseUp = () => {
      // Clear any pending long press timer
      if (longPressTimer) {
        clearTimeout(longPressTimer);
        setLongPressTimer(null);
      }

      if (isDragging) {
        setIsDragging(false);

        // Only save position if we were actually in drag mode
        if (isDragMode) {
          // No need to manually save to localStorage, the hook handles it
          setIsDragMode(false);
        }
      }
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // Touch events for mobile
    const handleTouchMove = (e) => {
      // Only handle touch move if we're in drag mode
      if (isDragging && isDragMode && e.touches[0]) {
        // Prevent default to stop scrolling only when in drag mode
        e.preventDefault();
        handleMouseMove({ clientX: e.touches[0].clientX, clientY: e.touches[0].clientY });
      }
      // Otherwise, let the default scroll behavior happen
    };

    // We need to use passive: false to be able to call preventDefault() when needed
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleMouseUp);
    document.addEventListener('touchcancel', handleMouseUp);

    // Cleanup
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleMouseUp);
      document.removeEventListener('touchcancel', handleMouseUp);

      // Clear any pending timer on unmount
      if (longPressTimer) {
        clearTimeout(longPressTimer);
      }
    };
  }, [isDragging, isDragMode, dragOffset, buttonPosition, longPressTimer, setButtonPosition]);

  // Fetch rider payouts
  const [authData] = useLocalStorage('authData', null);

  const fetchPayouts = useCallback(async () => {
    if (!riderId) return;

    setPayoutsLoading(true);
    try {
      let token = null;

      if (authData) {
        token = authData.token;
      }

      // Make the request with the token in headers
      const response = await axios.get(`${ApiUrl}/riders/${riderId}/payouts`, {
        headers: {
          Authorization: token,
        },
      });

      setPayouts(response.data);
      setErrorMessage('');
    } catch (err) {
      setErrorMessage('Failed to fetch payouts');
      toast.error(`Failed to fetch payouts: ${err.response?.data?.error || err.message}`);
    } finally {
      setPayoutsLoading(false);
    }
  }, [riderId, authData]);

  // Memoize filterOrders function with useCallback
  const filterOrders = useCallback((section) => {
    if (section === 'completed_payouts') {
      fetchPayouts();
      return;
    }

    const filtered = orders
      .filter((order) => order.status?.trim()?.toLowerCase() === section)
    // Sort by created_at in descending order
      .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

    if (filtered.length === 0) {
      setErrorMessage(`No ${section} orders found.`);
      setFilteredOrders([]);
    } else {
      setErrorMessage('');
      setFilteredOrders(filtered);
    }
  }, [orders, fetchPayouts]);

  // Update filtered orders when orders change or active section changes
  useEffect(() => {
    filterOrders(activeSection);
  }, [filterOrders, activeSection]);

  // Memoize getVendorInfo function to avoid recalculating for the same inputs
  const getVendorInfo = useMemo(() => (order, vendorsArray, field) => getVendorInfoBase(order,
    vendorsArray, field), []);

  // Memoize order counts for sidebar badges
  const orderCounts = useMemo(() => {
    if (!orders || orders.length === 0) {
      return {
        ready_for_pickup: 0,
        out_for_delivery: 0,
        delivered: 0,
        not_received: 0,
        received: 0,
      };
    }

    return {
      ready_for_pickup: orders.filter((order) => order.status === 'ready_for_pickup').length,
      out_for_delivery: orders.filter((order) => order.status === 'out_for_delivery').length,
      delivered: orders.filter((order) => order.status === 'delivered').length,
      not_received: orders.filter((order) => order.status === 'not_received').length,
      received: orders.filter((order) => order.status === 'received').length,
    };
  }, [orders]);

  // Function to toggle sidebar and handle overlay
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const confirmStatusUpdate = (orderId, newStatus) => {
    dispatch(updateOrderStatus({ orderId, status: newStatus, riderId }))
      .unwrap()
      .then(() => {
        dispatch(fetchOrders({ riderId }));
        if (newStatus === 'delivered') {
          toast.success('Order marked as delivered. Payment will be processed.');
        } else {
          toast.success(`Order status updated to ${newStatus}`);
        }
      })
      .catch(() => {
        toast.error('Error updating order status');
      })
      .finally(() => {
        setConfirmingOrderId(null);
      });
  };

  const handleStatusUpdate = (orderId, newStatus) => {
    // Show confirmation dialog if status is being changed to delivered
    if (newStatus === 'delivered') {
      setConfirmingOrderId(orderId);
      toast.info(
        <div className="delivery-confirmation">
          <p>Are you sure you want to mark this order as delivered?</p>
          <p>This will trigger payment processing to you and the vendor.</p>
          <div className="confirmation-buttons">
            <button
              type="button"
              onClick={() => {
                toast.dismiss();
                confirmStatusUpdate(orderId, newStatus);
              }}
              className="confirm-btn"
            >
              Confirm
            </button>
            <button
              type="button"
              onClick={() => {
                toast.dismiss();
                setConfirmingOrderId(null);
              }}
              className="cancel-btn"
            >
              Cancel
            </button>
          </div>
        </div>,
      );
    } else {
      confirmStatusUpdate(orderId, newStatus);
    }
  };

  // Toggle food description expansion
  const toggleDescription = (foodId) => {
    setExpandedDescriptions((prev) => ({
      ...prev,
      [foodId]: !prev[foodId],
    }));
  };

  // Auto-refresh function
  const handleRefresh = useCallback(async () => {
    if (riderId) {
      try {
        await Promise.all([
          dispatch(fetchOrders({ riderId })).unwrap(),
          dispatch(fetchVendors()).unwrap(),
        ]);
        // Silent refresh - no toast messages
      } catch (error) {
        throw new Error('Refresh error:', error);
      }
    }
  }, [dispatch, riderId]);

  return (
    <div className="rider-dashboard">
      {/* Background Overlay */}
      <div
        className={`dashboard-overlay ${!sidebarCollapsed ? 'active' : ''}`}
        onClick={() => setSidebarCollapsed(true)}
        role="presentation"
      />

      {/* Mobile Toggle Button */}
      <button
        className="dashboard-sidebar-toggle-mobile"
        onClick={toggleSidebar}
        type="button"
        aria-label="Toggle navigation"
      >
        {sidebarCollapsed ? <Menu /> : <X />}
      </button>

      {/* Sidebar Navigation */}
      <div className={`dashboard-sidebar ${sidebarCollapsed ? 'collapsed' : ''}`}>
        <div className="dashboard-sidebar-header">
          <div className="dashboard-logo">
            <Bike className="dashboard-logo-icon" />
            <span>EaseFood Rider</span>
          </div>
          <button
            type="button"
            className="dashboard-sidebar-toggle"
            onClick={toggleSidebar}
            aria-label="Toggle sidebar"
          >
            {sidebarCollapsed ? <Menu /> : <X />}
          </button>
        </div>

        <nav className="dashboard-nav">
          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'dashboard' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('dashboard');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <BarChart className="dashboard-nav-icon" />
            <span>Dashboard</span>
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'ready_for_pickup' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('ready_for_pickup');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <Package className="dashboard-nav-icon" />
            <span>Ready for Pickup</span>
            {orderCounts.ready_for_pickup > 0 && (
              <div className="dashboard-nav-badge">{orderCounts.ready_for_pickup}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'out_for_delivery' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('out_for_delivery');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <Truck className="dashboard-nav-icon" />
            <span>Out for Delivery</span>
            {orderCounts.out_for_delivery > 0 && (
              <div className="dashboard-nav-badge">{orderCounts.out_for_delivery}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'delivered' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('delivered');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <CheckCircle className="dashboard-nav-icon" />
            <span>Delivered Orders</span>
            {orderCounts.delivered > 0 && (
              <div className="dashboard-nav-badge">{orderCounts.delivered}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'not_received' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('not_received');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <XCircle className="dashboard-nav-icon" />
            <span>Not Received</span>
            {orderCounts.not_received > 0 && (
              <div className="dashboard-nav-badge">{orderCounts.not_received}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'received' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('received');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <CheckCircle className="dashboard-nav-icon" />
            <span>Received Orders</span>
            {orderCounts.received > 0 && (
              <div className="dashboard-nav-badge">{orderCounts.received}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'completed_payouts' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('completed_payouts');
              fetchPayouts();
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <DollarSign className="dashboard-nav-icon" />
            <span>Payouts</span>
            {payouts && payouts.length > 0 && (
              <div className="dashboard-nav-badge">{payouts.length}</div>
            )}
          </button>
        </nav>
      </div>

      {/* Main Content Area */}
      <div className={`dashboard-main ${sidebarCollapsed ? 'expanded' : ''}`}>
        {/* Pickup Notifications Panel Toggle Button */}
        <div
          className={`notifications-toggle ${isDragging ? 'dragging' : ''}`}
          style={{ top: buttonPosition.top, right: buttonPosition.right }}
          role="button"
          tabIndex={0}
          aria-label="Notification button"
          onKeyDown={(e) => {
            // Allow keyboard navigation for accessibility
            if (e.key === 'Enter' || e.key === ' ') {
              setShowNotificationsPanel(!showNotificationsPanel);
            }
          }}
        >
          <div className="notifications-buttons">
            <button
              type="button"
              className={`toggle-notifications-btn ${isDragMode ? 'draggable' : ''}`}
              onClick={() => {
                // Only toggle if not in drag mode
                if (!isDragMode) {
                  setShowNotificationsPanel(!showNotificationsPanel);
                }
              }}
              onMouseDown={(e) => {
                // Prevent default to avoid text selection
                e.preventDefault();

                // Set dragging state
                setIsDragging(true);

                // Calculate offset from where user clicked to the top-left corner of the button
                const buttonRect = e.currentTarget.getBoundingClientRect();
                setDragOffset({
                  x: e.clientX - buttonRect.left,
                  y: e.clientY - buttonRect.top,
                });

                // Start a timer for long press (500ms)
                const timer = setTimeout(() => {
                  // Enter drag mode after long press
                  setIsDragMode(true);
                }, 500);

                setLongPressTimer(timer);
              }}
              onTouchStart={(e) => {
                if (e.touches[0]) {
                  // Set dragging state but don't prevent default scrolling yet
                  setIsDragging(true);

                  // Calculate offset from where user touched to the top-left corner of the button
                  const buttonRect = e.currentTarget.getBoundingClientRect();
                  setDragOffset({
                    x: e.touches[0].clientX - buttonRect.left,
                    y: e.touches[0].clientY - buttonRect.top,
                  });

                  // Start a timer for long press (500ms)
                  const timer = setTimeout(() => {
                    // Enter drag mode after long press
                    setIsDragMode(true);
                  }, 500);

                  setLongPressTimer(timer);
                }
              }}
            >
              {showNotificationsPanel ? 'Hide Notifications' : 'Show Notifications'}
              {!showNotificationsPanel && (
              <span className={`notification-badge ${orders.filter((order) => order.status === 'ready_for_pickup' && !viewedOrderIds.includes(order.id)).length > 0 ? 'has-notifications' : 'no-notifications'}`}>
                {orders.filter((order) => order.status === 'ready_for_pickup' && !viewedOrderIds.includes(order.id)).length}
              </span>
              )}
            </button>
            {viewedOrderIds.length > 0 && (
            <button
              type="button"
              className="reset-notifications-btn"
              onClick={() => {
                // Only reset if not dragging
                if (!isDragging) {
                  setViewedOrderIds([]);
                  localStorage.removeItem('viewedOrderIds');
                  toast.info('Notification history cleared');
                }
              }}
              title="Reset notification history"
            >
              ↺
            </button>
            )}
          </div>
        </div>

        {/* Pickup Notifications Panel */}
        {showNotificationsPanel && (
        <PickupNotificationsPanel
          viewedOrderIds={viewedOrderIds}
          onViewOrder={(orderId) => {
            // Add this order to viewed orders and save to localStorage
            setViewedOrderIds((prev) => {
              const newViewedOrders = [...prev, orderId];
              localStorage.setItem('viewedOrderIds', JSON.stringify(newViewedOrders));
              return newViewedOrders;
            });

            // Set active section to ready_for_pickup
            setActiveSection('ready_for_pickup');
            filterOrders('ready_for_pickup');

            // Hide the panel when viewing an order
            setShowNotificationsPanel(false);

            // Scroll to the order with the given ID
            setTimeout(() => {
              const orderElement = document.getElementById(`order-${orderId}`);
              if (orderElement) {
                orderElement.scrollIntoView({ behavior: 'smooth' });
                orderElement.classList.add('highlight-order');
                setTimeout(() => {
                  orderElement.classList.remove('highlight-order');
                }, 3000);
              }
            }, 500);
          }}
        />
        )}

        {/* Mobile Notification Badge */}
        <MobileNotificationBadge
          onTogglePanel={() => setShowNotificationsPanel(!showNotificationsPanel)}
          showPanel={showNotificationsPanel}
          userRole="rider"
          position={{ bottom: '80px', right: '20px' }}
        />

        {/* Dashboard Header with Wave Banner */}
        <div className="vendors-dashboard-header">
          <WaveBanner
            type="rider"
            title="Rider Dashboard"
            subtitle="Deliver {dynamicText} across Dunkwa Offin"
            dynamicTexts={[
              'hot meals',
              'orders',
              'happiness',
              'flavors',
              'happy plates',
              'fuel for the day',
            ]}
          />
          <button
            type="button"
            className="tutorial-trigger-btn"
            onClick={() => setShowTutorial(true)}
            title="Show dashboard tutorial"
          >
            <HelpCircle size={20} />
            <span>Help</span>
          </button>
        </div>

        <div className="vendors-dashboard-content">
          {activeSection === 'dashboard' && (
            <RiderStatisticsSection />
          )}

          {/* Auto-refresh component */}
          <div className="dash">
            <AutoRefresh
              onRefresh={handleRefresh}
              interval={300000} // 5 minutes
              disabled={!riderId}
            />
          </div>

          <div className="data-container">
            {activeSection !== 'dashboard' && status === 'loading' && <p>Loading...</p>}
            {activeSection !== 'dashboard' && error && (
            <p className="error-message error">
              Error fetching orders:
              {' '}
              {error}
            </p>
            )}
            {activeSection !== 'dashboard' && errorMessage && <p className="error-message error">{errorMessage}</p>}

            {activeSection === 'completed_payouts' ? (
              <div className="payouts-container">

                {payoutsLoading && <p>Loading payouts...</p>}
                {!payoutsLoading && payouts.length === 0 && <p>No payouts found.</p>}
                {!payoutsLoading && payouts.length > 0 && (
                <div className="payouts-list">
                  <h3>
                    <DollarSign size={20} style={{ verticalAlign: 'middle', marginRight: '0.5rem' }} />
                    Your Payouts
                  </h3>
                  <table className="payouts-table">
                    <thead>
                      <tr>
                        <th>Order ID</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Date</th>
                      </tr>
                    </thead>
                    <tbody>
                      {payouts.map((payout) => (
                        <tr key={payout.id} className={`payout-row ${payout.status}`}>
                          <td>{payout.order_id}</td>
                          <td>
                            GH₵
                            {payout.amount}
                          </td>
                          <td className="vendors-payment-status">
                            <span className={`vendors-status-value ${payout.status}`}>{payout.status}</span>
                          </td>
                          <td>{convertToLocalTime(payout.created_at)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                )}
              </div>
            ) : (
              <div className="vendors-orders-grid">
                {filteredOrders.map((order) => {
                  // Access display_recipient for each individual order
                  const recipientInfo = order.display_recipient;

                  return (
                    <div key={order.id} id={`order-${order.id}`} className="vendors-order-card">
                      <div className="vendors-order-header">
                        <h3 className="vendors-order-id">
                          Order #
                          {order.id}
                        </h3>
                        <span className="vendors-order-time">
                          Ordered At:
                          {' '}
                          {convertToLocalTime(order.created_at)}
                        </span>
                      </div>

                      <div className="vendors-order-content">
                        <div className="vendors-order-details">
                          <h5 className="riders-section-heading">Customer Details</h5>
                          <div className="vendors-detail-row riders-detail-row">
                            <span className="vendors-detail-label riders-detail-label">
                              <User size={14} className="riders-detail-icon" />
                              {' '}
                              Name
                            </span>
                            <span className="vendors-detail-value riders-detail-value">{recipientInfo?.name || 'Loading...'}</span>
                          </div>
                          <div className="vendors-detail-row riders-detail-row">
                            <span className="vendors-detail-label riders-detail-label">
                              <Phone size={14} className="riders-detail-icon" />
                              {' '}
                              Phone
                            </span>
                            <span className="vendors-detail-value riders-detail-value">{recipientInfo?.phone || 'Not available'}</span>
                          </div>
                          <div className="vendors-detail-row riders-detail-row">
                            <span className="vendors-detail-label riders-detail-label">
                              <MapPin size={14} className="riders-detail-icon" />
                              {' '}
                              Address
                            </span>
                            <span className="vendors-detail-value riders-detail-value">{recipientInfo?.address}</span>
                          </div>
                          <h5 className="riders-section-heading">Vendor Details</h5>
                          <div className="vendors-detail-row riders-detail-row">
                            <span className="vendors-detail-label riders-detail-label">
                              <ChefHat size={14} className="riders-detail-icon" />
                              {' '}
                              Name
                            </span>
                            <span className="vendors-detail-value riders-detail-value">
                              {getVendorInfo(order, vendors, 'name') || 'Unknown vendor'}
                            </span>
                          </div>
                          <div className="vendors-detail-row riders-detail-row">
                            <span className="vendors-detail-label riders-detail-label">
                              <Phone size={14} className="riders-detail-icon" />
                              {' '}
                              Phone
                            </span>
                            <span className="vendors-detail-value riders-detail-value">
                              {getVendorInfo(order, vendors, 'phone') || 'No phone available'}
                            </span>
                          </div>
                          <div className="vendors-detail-row riders-detail-row">
                            <span className="vendors-detail-label riders-detail-label">
                              <MapPin size={14} className="riders-detail-icon" />
                              {' '}
                              Address
                            </span>
                            <span className="vendors-detail-value riders-detail-value">
                              {getVendorInfo(order, vendors, 'address') || 'Check vendor details'}
                            </span>
                          </div>
                          <h5 className="riders-section-heading">Order Details</h5>
                          <div className="vendors-detail-row riders-detail-row">
                            <span className="vendors-detail-label riders-detail-label">
                              <Tag size={14} className="riders-detail-icon" />
                              {' '}
                              Status
                            </span>
                            <span className="vendors-detail-value riders-detail-value">
                              <span className={`vendors-status-value ${order.status}`}>{order.status}</span>
                            </span>
                          </div>
                          <div className="vendors-detail-row riders-detail-row">
                            <span className="vendors-detail-label riders-detail-label">
                              <Tag size={14} className="riders-detail-icon" />
                              {' '}
                              Update Status
                            </span>
                            <span className="vendors-detail-value riders-detail-value">
                              <select
                                value={order.status ?? ''}
                                onChange={(e) => handleStatusUpdate(order.id, e.target.value)}
                                className="vendors-status-select"
                                disabled={confirmingOrderId === order.id}
                              >
                                <option value="out_for_delivery">Out for Delivery</option>
                                <option value="delivered">Delivered</option>
                              </select>
                              {confirmingOrderId === order.id && (
                              <span className="vendors-processing-status">Processing...</span>
                              )}
                            </span>
                          </div>
                          <div className="vendors-detail-row riders-detail-row">
                            <span className="vendors-detail-label riders-detail-label">
                              <Clock size={14} className="riders-detail-icon" />
                              {' '}
                              Time
                            </span>
                            <span className="vendors-detail-value riders-detail-value">{convertToLocalTime(order.created_at)}</span>
                          </div>
                        </div>

                        <div className="vendors-ordered-foods">
                          <h4>Ordered Items</h4>
                          {order.foods?.map((food, index) => (
                            <div key={food.id} className="vendors-food-item">
                              <img
                                src={food.food_image_url}
                                alt={food.name}
                                className="vendors-food-img"
                                onError={(e) => {
                                  e.target.onerror = null;
                                }}
                              />
                              <div className="vendors-food-details">
                                <span className="vendors-food-name">{food.name}</span>
                                {food.description && (
                                <div className="vendors-food-description">
                                  <p>
                                    {expandedDescriptions[food.id]
                                      ? food.description
                                      : `${food.description.substring(0, 80)}${food.description.length > 80 ? '...' : ''}`}
                                  </p>
                                  {food.description.length > 80 && (
                                  <button
                                    type="button"
                                    className="vendors-description-toggle"
                                    onClick={() => toggleDescription(food.id)}
                                  >
                                    {expandedDescriptions[food.id] ? 'See less' : 'See more'}
                                  </button>
                                  )}
                                </div>
                                )}
                                <div className="vendors-food-meta">
                                  <span className="vendors-food-price">
                                    GH₵
                                    {food.price}
                                  </span>
                                  <span className="vendors-food-quantity">
                                    Qty:
                                    {order.quantities && index < order.quantities.length ? order.quantities[index] : 'N/A'}
                                  </span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Dashboard Tutorial */}
      <DashboardTutorial
        isVisible={showTutorial}
        onClose={() => setShowTutorial(false)}
        onSkip={() => setShowTutorial(false)}
        tutorialSteps={tutorialSteps}
      />
    </div>
  );
};

export default RidersDashboard;
