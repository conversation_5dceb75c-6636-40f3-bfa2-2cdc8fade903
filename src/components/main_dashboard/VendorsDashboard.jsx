import {
  useEffect, useState, useCallback, lazy, Suspense, useMemo,
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import axios from 'axios';
import { toast } from 'react-toastify';
import {
  Clipboard,
  Clock,
  Package,
  Truck,
  CheckCircle,
  User,
  MapPin,
  Phone,
  DollarSign,
  AlertCircle,
  ShoppingBag,
  Tag,
  Menu,
  X,
  Utensils,
  BarChart,
  XCircle,
  HelpCircle,
  Bell,
  RotateCcw,
  Edit3,
  Trash2,
} from 'lucide-react';
import useLocalStorage from '../../hooks/useLocalStorage';
import WaveBanner from './WaveBanner';
import { fetchOrders, updateOrderStatus } from '../../redux/slice/ordersSlice';
import {
  fetchFoods, selectFoods, selectFoodsStatus, selectFoodsError, updateFood, deleteFood,
} from '../../redux/slice/foodsSlice';
import convertToLocalTime from '../helper-functions/convertToLocalTime';
import ApiUrl from '../helper-functions/ApiUrl';
import Loader from '../helper-functions/Loader';
import AutoRefresh from '../helper-functions/AutoRefresh';
import DashboardTutorial from '../helper-functions/DashboardTutorial';
import MobileNotificationBadge from '../notifications/MobileNotificationBadge';
import { formatPriceRange } from '../../utils/priceUtils';
import VendorAvailabilityToggle from '../helper-functions/VendorAvailabilityToggle';
import {
  selectCurrentVendor,
  selectAvailabilityStatus,
  selectAvailabilityError,
  setCurrentVendor,
} from '../../redux/slice/vendersSlice';

import '../notifications/PickupNotificationsPanel.css';

// Lazy load components
const AddFoodForm = lazy(() => import('../helper-functions/AddFoodForm'));
const PickupNotificationsPanel = lazy(() => import('../notifications/PickupNotificationsPanel'));
const VendorStatisticsSection = lazy(() => import('./VendorStatisticsSection'));

const VendorsDashboard = () => {
  const dispatch = useDispatch();
  const orders = useSelector((state) => state.orders.orders);
  const status = useSelector((state) => state.orders.status);
  const error = useSelector((state) => state.orders.error);

  const { user } = useSelector((state) => state.auth);
  const vendorId = user?.id;

  const foods = useSelector(selectFoods);
  const foodsStatus = useSelector(selectFoodsStatus);
  const foodsError = useSelector(selectFoodsError);

  const currentVendor = useSelector(selectCurrentVendor);
  const availabilityStatus = useSelector(selectAvailabilityStatus);
  const availabilityError = useSelector(selectAvailabilityError);

  const [showAddFoodForm, setShowAddFoodForm] = useState(false);
  const [expandedDescriptions, setExpandedDescriptions] = useState({});
  const [activeSection, setActiveSection] = useState('confirmed');
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [errorMessage, setErrorMessage] = useState('');
  const [payouts, setPayouts] = useState([]);
  const [payoutsLoading, setPayoutsLoading] = useState(false);
  const [orderPayouts, setOrderPayouts] = useState({});
  const [showNotificationsPanel, setShowNotificationsPanel] = useState(true);
  // Set sidebar collapsed by default on mobile
  const [sidebarCollapsed, setSidebarCollapsed] = useState(window.innerWidth <= 768);

  // Edit Food modal state.
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [foodBeingEdited, setFoodBeingEdited] = useState(null);
  const [editName, setEditName] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [editPriceMode, setEditPriceMode] = useState('single'); // 'single' | 'multiple'
  const [editSinglePrice, setEditSinglePrice] = useState('');
  const [editPrices, setEditPrices] = useState([]);
  const [isSaving, setIsSaving] = useState(false);

  // Delete confirmation modal state
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [pendingDeleteFood, setPendingDeleteFood] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    if (user && !currentVendor) {
      dispatch(setCurrentVendor(user));
    }
  }, [user, currentVendor, dispatch]);

  const openEditModal = (food) => {
    setFoodBeingEdited(food);
    setEditName(food.name || '');
    setEditDescription(food.description || '');
    // Determine mode by prices array length
    const arr = (food.prices && food.prices.length > 0) ? food.prices : (food.price ? [food.price] : []);
    if (arr.length <= 1) {
      setEditPriceMode('single');
      setEditSinglePrice(String(arr[0] || ''));
      setEditPrices([]);
    } else {
      setEditPriceMode('multiple');
      setEditPrices(arr.map((p) => String(p)));
      setEditSinglePrice('');
    }
    setEditModalOpen(true);
  };

  const closeEditModal = () => {
    setEditModalOpen(false);
    setFoodBeingEdited(null);
    setEditName('');
    setEditDescription('');
    setEditPriceMode('single');
    setEditSinglePrice('');
    setEditPrices([]);
  };

  const handleSaveFoodEdits = async () => {
    if (!foodBeingEdited || isSaving) return;

    const formData = new FormData();
    formData.append('food[name]', editName);
    formData.append('food[description]', editDescription);

    if (editPriceMode === 'single') {
      formData.append('food[price]', editSinglePrice || '0');
    } else {
      // send array
      editPrices.forEach((p) => {
        if (p !== '') formData.append('food[prices][]', p);
      });
    }

    try {
      setIsSaving(true);
      await dispatch(updateFood({ vendorId, foodId: foodBeingEdited.id, formData })).unwrap();
      toast.success('Food updated');
      closeEditModal();
    } catch (e) {
      toast.error('Failed to update food');
    } finally {
      setIsSaving(false);
    }
  };

  // Delete confirmation flow
  const promptDeleteFood = (food) => {
    setPendingDeleteFood(food);
    setDeleteModalOpen(true);
  };
  const cancelDeleteFood = () => {
    setDeleteModalOpen(false);
    setPendingDeleteFood(null);
    setIsDeleting(false);
  };
  const confirmDeleteFood = async () => {
    if (!pendingDeleteFood || isDeleting) return;
    try {
      setIsDeleting(true);
      await dispatch(deleteFood({ vendorId, foodId: pendingDeleteFood.id })).unwrap();
      toast.success('Food deleted');
      cancelDeleteFood();
    } catch (e) {
      toast.error('Failed to delete food');
      setIsDeleting(false);
    }
  };

  // Initialize viewedOrderIds using custom hook
  const [viewedOrderIds, setViewedOrderIds] = useLocalStorage('vendorViewedOrderIds', []);

  // State for draggable notification toggle button position
  const [buttonPosition, setButtonPosition] = useLocalStorage('vendorNotificationButtonPosition', { top: '0.625rem', right: '0.625rem' });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [longPressTimer, setLongPressTimer] = useState(null);
  const [isDragMode, setIsDragMode] = useState(false);

  // Use custom hook for last reset date
  const [lastResetDate, setLastResetDate] = useLocalStorage('vendorLastViewedOrdersReset', '');

  // Tutorial state
  const [showTutorial, setShowTutorial] = useLocalStorage('vendorTutorialShown', false);

  // Tutorial steps for Vendors Dashboard
  const tutorialSteps = [
    {
      title: 'Welcome to Your Vendor Dashboard!',
      description: "Let's take a quick tour to help you manage your restaurant and orders effectively.",
      icon: <Utensils size={48} />,
      details: 'This tutorial will show you how to manage orders, update your menu, and track your earnings.',
    },
    {
      title: 'Navigation Sidebar',
      description: 'Use the sidebar to switch between different sections of your dashboard.',
      targetSelector: '.dashboard-sidebar',
      icon: <Menu size={48} />,
      details: "• Dashboard: View your statistics and overview\n• Not Received: Orders that weren't delivered\n• Received: Successfully delivered orders\n• Pending: Unpaid orders in customer carts\n• Confirmed: Paid orders ready for processing\n• Processing: Orders being prepared\n• Ready for Pickup: Orders ready for riders\n• Delivered: Completed orders\n• Add Food: Add new menu items\n• View Foods: Manage your menu\n• Payouts: Your earnings and payment history",
    },
    {
      title: 'Notifications Panel',
      description: 'Stay updated with real-time notifications about new confirmed orders.',
      targetSelector: '.notifications-toggle',
      icon: <Bell size={48} />,
      details: '• Click to show/hide notifications\n• Drag to reposition (long press on mobile)\n• Badge shows number of unread notifications\n• Reset button clears notification history',
    },
    {
      title: 'Auto-Refresh Feature',
      description: 'Your dashboard automatically updates every 5 minutes to keep data fresh.',
      targetSelector: '.auto-refresh-container',
      icon: <RotateCcw size={48} />,
      details: '• Data refreshes automatically in the background\n• Click the button to refresh manually\n• Progress ring shows time until next refresh\n• No page reloads or interruptions',
    },
    {
      title: 'Order Management',
      description: 'View order details, customer information, and update order status.',
      targetSelector: '.vendors-orders-grid',
      icon: <Package size={48} />,
      details: '• View customer details and delivery address\n• See order items and quantities\n• Update order status as you progress\n• Process orders from confirmed to delivered',
    },
    {
      title: 'Status Updates',
      description: 'Keep customers informed by updating order status throughout the process.',
      targetSelector: '.vendors-status-select',
      icon: <CheckCircle size={48} />,
      details: "• 'Processing': When you start preparing the order\n• 'Ready for Pickup': When order is ready for riders\n• Status changes trigger notifications to customers\n• Track order progress from start to finish",
    },
    {
      title: 'Menu Management',
      description: 'Add and manage your food menu items to attract more customers.',
      targetSelector: '.dashboard-nav-item',
      icon: <ShoppingBag size={48} />,
      details: '• Add Food: Create new menu items with images and descriptions\n• View Foods: See all your current menu items\n• Update prices and descriptions as needed\n• Keep your menu fresh and appealing',
    },
    {
      title: "You're All Set!",
      description: 'You now know how to navigate your vendor dashboard effectively.',
      icon: <Utensils size={48} />,
      details: '• Check notifications regularly for new orders\n• Update order status promptly\n• Keep your menu updated and appealing\n• Monitor your earnings in the payouts section\n\nHappy cooking! 👨‍🍳',
    },
  ];

  // Fetch orders and foods when the component mounts
  useEffect(() => {
    if (vendorId) {
      // Check if we need to reset viewed orders (e.g., new day)
      const today = new Date().toDateString();

      if (lastResetDate !== today) {
        // Clear viewed orders at the start of a new day
        setViewedOrderIds([]);
        setLastResetDate(today);
      }
    }
  }, [vendorId, lastResetDate, setViewedOrderIds, setLastResetDate]);

  // Fetch orders and foods when vendorId is available
  useEffect(() => {
    if (vendorId && status === 'idle') {
      dispatch(fetchOrders({ vendorId }));
      dispatch(fetchFoods(vendorId));
    }
  }, [dispatch, vendorId, status]);

  // Handle responsive sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      // Only auto-collapse on mobile
      if (window.innerWidth <= 768) {
        setSidebarCollapsed(true);
      }
      // On desktop, we don't auto-expand anymore to allow user preference
      // This allows the sidebar to stay collapsed if the user wants it that way
    };

    // Set initial state
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle mouse move and mouse up events for dragging
  useEffect(() => {
    const handleMouseMove = (e) => {
      // Only handle mouse move if we're in drag mode
      if (isDragging && isDragMode) {
        // Calculate new position based on mouse position and initial offset
        const newRight = Math.max(0, window.innerWidth - e.clientX - dragOffset.x);
        const newTop = Math.max(0, e.clientY - dragOffset.y);

        // Convert to rem units
        const remRight = `${newRight / 16}rem`;
        const remTop = `${newTop / 16}rem`;

        // Update position
        setButtonPosition({ top: remTop, right: remRight });
      }
    };

    const handleMouseUp = () => {
      // Clear any pending long press timer
      if (longPressTimer) {
        clearTimeout(longPressTimer);
        setLongPressTimer(null);
      }

      if (isDragging) {
        setIsDragging(false);

        // Only save position if we were actually in drag mode
        if (isDragMode) {
          // No need to manually save to localStorage, the hook handles it
          setIsDragMode(false);
        }
      }
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // Touch events for mobile
    const handleTouchMove = (e) => {
      // Only handle touch move if we're in drag mode
      if (isDragging && isDragMode && e.touches[0]) {
        // Prevent default to stop scrolling only when in drag mode
        e.preventDefault();
        handleMouseMove({ clientX: e.touches[0].clientX, clientY: e.touches[0].clientY });
      }
      // Otherwise, let the default scroll behavior happen.
    };

    // We need to use passive: false to be able to call preventDefault() when needed.
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleMouseUp);
    document.addEventListener('touchcancel', handleMouseUp);

    // Cleanup
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleMouseUp);
      document.removeEventListener('touchcancel', handleMouseUp);

      // Clear any pending timer on unmount.
      if (longPressTimer) {
        clearTimeout(longPressTimer);
      }
    };
  }, [isDragging, isDragMode, dragOffset, buttonPosition, longPressTimer, setButtonPosition]);

  // Fetch vendor payouts.
  const [authData] = useLocalStorage('authData', null);

  const fetchPayouts = useCallback(async () => {
    if (!vendorId) return;

    setPayoutsLoading(true);
    try {
      let token = null;

      if (authData) {
        token = authData.token;
      }

      // Make the request with the token in headers.
      const response = await axios.get(`${ApiUrl}/vendors/${vendorId}/payouts`, {
        headers: {
          Authorization: token,
        },
      });

      setPayouts(response.data);

      // Create a map of order_id to payout for easy lookup.
      const payoutsMap = {};
      response.data.forEach((payout) => {
        payoutsMap[payout.order_id] = payout;
      });
      setOrderPayouts(payoutsMap);

      setErrorMessage('');
    } catch (err) {
      setErrorMessage('Failed to fetch payouts');
      toast.error(`Failed to fetch payouts: ${err.response?.data?.error || err.message}`);
    } finally {
      setPayoutsLoading(false);
    }
  }, [vendorId, authData]);

  // Initial filtering when orders are loaded
  useEffect(() => {
    if (orders) {
      // Filter orders based on the active section
      const confirmedOrders = orders.filter((order) => order.status?.trim()?.toLowerCase() === 'confirmed')
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

      if (confirmedOrders.length === 0) {
        setErrorMessage('No confirmed orders found.');
        setFilteredOrders([]);
      } else {
        setErrorMessage('');
        setFilteredOrders(confirmedOrders);
      }

      // Fetch payouts to get payment status for delivered orders
      if (orders.some((order) => order.status === 'delivered')) {
        fetchPayouts();
      }
    }
  }, [orders, fetchPayouts]);

  // Filter orders when active section changes
  useEffect(() => {
    if (orders && orders.length > 0) {
      // Skip for non-order sections
      if (activeSection === 'addFood' || activeSection === 'foods' || activeSection === 'payouts') {
        return;
      }

      const filteredByStatus = orders.filter((order) => order.status?.trim()
        ?.toLowerCase() === activeSection)
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

      if (filteredByStatus.length === 0) {
        setErrorMessage(`No ${activeSection} orders found.`);
        setFilteredOrders([]);
      } else {
        setErrorMessage('');
        setFilteredOrders(filteredByStatus);
      }
    }
  }, [activeSection, orders]);

  const confirmStatusUpdate = (orderId, newStatus) => {
    dispatch(updateOrderStatus({ orderId, status: newStatus, vendorId }))
      .unwrap() // unwrap the promise returned by dispatch to handle success/failure
      .then(() => {
        // Once the status update is successful, re-fetch orders
        dispatch(fetchOrders({ vendorId }));

        // Show success message
        if (newStatus === 'ready_for_pickup') {
          toast.success('Rider has been notified that the order is ready for pickup!');
        } else {
          toast.success(`Order status updated to ${newStatus}`);
        }
      })
      .catch((error) => {
        toast.error('Error updating order status', error);
      });
  };

  const handleStatusUpdate = (orderId, newStatus) => {
    const order = orders.find((o) => o.id === orderId);

    // Defensive check: don't allow changing status if it's already 'received'
    if (order?.status === 'received') {
      toast.info('This order is locked and cannot be changed because it has been marked as received.');
      return;
    }
    // Show confirmation dialog if status is being changed to ready_for_pickup
    if (newStatus === 'ready_for_pickup') {
      if (order && order.rider) {
        toast.info(
          <div className="status-change-confirmation">
            <p>This will notify the rider that the order is ready for pickup.</p>
            <div className="confirmation-buttons">
              <button
                type="button"
                onClick={() => {
                  toast.dismiss();
                  confirmStatusUpdate(orderId, newStatus);
                }}
                className="confirm-btn"
              >
                Confirm
              </button>
              <button
                type="button"
                onClick={() => toast.dismiss()}
                className="cancel-btn"
              >
                Cancel
              </button>
            </div>
          </div>,
          {
            autoClose: false,
            closeOnClick: false,
            position: 'top-center',
          },
        );
      } else {
        confirmStatusUpdate(orderId, newStatus);
      }
    } else {
      confirmStatusUpdate(orderId, newStatus);
    }
  };

  const sections = [
    {
      label: 'Dashboard',
      key: 'dashboard',
      onClick: () => setActiveSection('dashboard'),
      icon: <BarChart className="dashboard-nav-icon" />,
    },
    {
      label: 'Not Received Orders',
      key: 'not_received',
      onClick: () => setActiveSection('not_received'),
    },
    {
      label: 'Received Orders',
      key: 'received',
      onClick: () => setActiveSection('received'),
    },
    {
      label: 'Pending Orders',
      key: 'pending',
      onClick: () => setActiveSection('pending'),
    },
    {
      label: 'Confirmed Orders',
      key: 'confirmed',
      onClick: () => setActiveSection('confirmed'),
    },
    {
      label: 'Processing Orders',
      key: 'processing',
      onClick: () => setActiveSection('processing'),
    },
    {
      label: 'Ready for Pickup',
      key: 'ready_for_pickup',
      onClick: () => setActiveSection('ready_for_pickup'),
    },
    {
      label: 'Delivered Orders',
      key: 'delivered',
      onClick: () => {
        setActiveSection('delivered');
        // Fetch payouts to get payment status
        fetchPayouts();
      },
    },
    {
      label: 'Add Food',
      key: 'addFood',
      onClick: () => {
        setActiveSection('addFood');
        setShowAddFoodForm(true);
      },
    },
    { label: 'View Foods', key: 'foods', onClick: () => setActiveSection('foods') },
    {
      label: 'Payouts',
      key: 'payouts',
      onClick: () => {
        setActiveSection('payouts');
        fetchPayouts();
      },
    },
  ];

  // Memoize order counts for sidebar badges
  const orderCounts = useMemo(() => {
    if (!orders || orders.length === 0) {
      return {
        not_received: 0,
        received: 0,
        pending: 0,
        confirmed: 0,
        processing: 0,
        ready_for_pickup: 0,
        delivered: 0,
      };
    }

    return {
      not_received: orders.filter((order) => order.status === 'not_received').length,
      received: orders.filter((order) => order.status === 'received').length,
      pending: orders.filter((order) => order.status === 'pending').length,
      confirmed: orders.filter((order) => order.status === 'confirmed').length,
      processing: orders.filter((order) => order.status === 'processing').length,
      ready_for_pickup: orders.filter((order) => order.status === 'ready_for_pickup').length,
      delivered: orders.filter((order) => order.status === 'delivered').length,
    };
  }, [orders]);

  // Function to toggle sidebar and handle overlay
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // Handle notification view
  const handleViewOrder = (orderId) => {
    // Add this order to viewed orders - the hook handles saving to localStorage
    setViewedOrderIds((prev) => [...prev, orderId]);

    // Set active section to confirmed
    setActiveSection('confirmed');

    // Hide the panel when viewing an order
    setShowNotificationsPanel(false);

    // Scroll to the order with the given ID
    setTimeout(() => {
      const orderElement = document.getElementById(`order-${orderId}`);
      if (orderElement) {
        orderElement.scrollIntoView({ behavior: 'smooth' });
        orderElement.classList.add('highlight-order');
        setTimeout(() => {
          orderElement.classList.remove('highlight-order');
        }, 3000);
      }
    }, 500);
  };

  // Toggle food description expansion
  const toggleDescription = (foodId) => {
    setExpandedDescriptions((prev) => ({
      ...prev,
      [foodId]: !prev[foodId],
    }));
  };

  // Auto-refresh function
  const handleRefresh = useCallback(async () => {
    if (vendorId) {
      try {
        await Promise.all([
          dispatch(fetchOrders({ vendorId })).unwrap(),
          dispatch(fetchFoods(vendorId)).unwrap(),
        ]);
        // Silent refresh - no toast messages
      } catch (error) {
        console.error('Refresh error:', error);
      }
    }
  }, [dispatch, vendorId]);

  return (
    <div className="vendor-dashboard">
      {/* Background Overlay */}
      <div
        className={`dashboard-overlay ${!sidebarCollapsed ? 'active' : ''}`}
        onClick={() => setSidebarCollapsed(true)}
        role="presentation"
      />

      {/* Mobile Toggle Button */}
      <button
        className="dashboard-sidebar-toggle-mobile"
        onClick={toggleSidebar}
        type="button"
        aria-label="Toggle navigation"
      >
        {sidebarCollapsed ? <Menu /> : <X />}
      </button>

      {/* Sidebar Navigation */}
      <div className={`dashboard-sidebar ${sidebarCollapsed ? 'collapsed' : ''}`}>
        <div className="dashboard-sidebar-header">
          <div className="dashboard-logo">
            <Utensils className="dashboard-logo-icon" />
            <span>EaseFood Vendor</span>
          </div>
          <button
            type="button"
            className="dashboard-sidebar-toggle"
            onClick={toggleSidebar}
            aria-label="Toggle sidebar"
          >
            {sidebarCollapsed ? <Menu /> : <X />}
          </button>
        </div>

        <nav className="dashboard-nav">
          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'dashboard' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('dashboard');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <BarChart className="dashboard-nav-icon" />
            <span>Dashboard</span>
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'not_received' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('not_received');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <XCircle className="dashboard-nav-icon" />
            <span>Not Received Orders</span>
            {orderCounts.not_received > 0 && (
              <div className="dashboard-nav-badge">{orderCounts.not_received}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'received' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('received');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <CheckCircle className="dashboard-nav-icon" />
            <span>Received Orders</span>
            {orderCounts.received > 0 && (
              <div className="dashboard-nav-badge">{orderCounts.received}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'pending' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('pending');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <ShoppingBag className="dashboard-nav-icon" />
            <span>Pending Orders</span>
            {orderCounts.pending > 0 && (
              <div className="dashboard-nav-badge">{orderCounts.pending}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'confirmed' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('confirmed');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <Clipboard className="dashboard-nav-icon" />
            <span>Confirmed Orders</span>
            {orderCounts.confirmed > 0 && (
              <div className="dashboard-nav-badge">{orderCounts.confirmed}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'processing' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('processing');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <Clock className="dashboard-nav-icon" />
            <span>Processing Orders</span>
            {orderCounts.processing > 0 && (
              <div className="dashboard-nav-badge">{orderCounts.processing}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'ready_for_pickup' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('ready_for_pickup');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <Package className="dashboard-nav-icon" />
            <span>Ready for Pickup</span>
            {orderCounts.ready_for_pickup > 0 && (
              <div className="dashboard-nav-badge">{orderCounts.ready_for_pickup}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'delivered' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('delivered');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
              fetchPayouts();
            }}
          >
            <Truck className="dashboard-nav-icon" />
            <span>Delivered Orders</span>
            {orderCounts.delivered > 0 && (
              <div className="dashboard-nav-badge">{orderCounts.delivered}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'addFood' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('addFood');
              setShowAddFoodForm(true);
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <ShoppingBag className="dashboard-nav-icon" />
            <span>Add Food</span>
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'foods' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('foods');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <Tag className="dashboard-nav-icon" />
            <span>View Foods</span>
            {foods && foods.length > 0 && (
              <div className="dashboard-nav-badge">{foods.length}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'payouts' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('payouts');
              fetchPayouts();
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <DollarSign className="dashboard-nav-icon" />
            <span>Payouts</span>
            {payouts && payouts.length > 0 && (
              <div className="dashboard-nav-badge">{payouts.length}</div>
            )}
          </button>
        </nav>
      </div>

      {/* Main Content Area */}
      <div className={`dashboard-main ${sidebarCollapsed ? 'expanded' : ''}`}>
        {/* Pickup Notifications Panel Toggle Button */}
        <div
          className={`notifications-toggle ${isDragging ? 'dragging' : ''}`}
          style={{ top: buttonPosition.top, right: buttonPosition.right }}
          role="button"
          tabIndex={0}
          aria-label="Notification button"
          onKeyDown={(e) => {
            // Allow keyboard navigation for accessibility
            if (e.key === 'Enter' || e.key === ' ') {
              setShowNotificationsPanel(!showNotificationsPanel);
            }
          }}
        >
          <div className="notifications-buttons">
            <button
              type="button"
              className={`toggle-notifications-btn ${isDragMode ? 'draggable' : ''}`}
              onClick={() => {
                // Only toggle if not in drag mode
                if (!isDragMode) {
                  setShowNotificationsPanel(!showNotificationsPanel);
                }
              }}
              onMouseDown={(e) => {
                // Prevent default to avoid text selection
                e.preventDefault();

                // Set dragging state
                setIsDragging(true);

                // Calculate offset from where user clicked to the top-left corner of the button
                const buttonRect = e.currentTarget.getBoundingClientRect();
                setDragOffset({
                  x: e.clientX - buttonRect.left,
                  y: e.clientY - buttonRect.top,
                });

                // Start a timer for long press (500ms)
                const timer = setTimeout(() => {
                  // Enter drag mode after long press
                  setIsDragMode(true);
                }, 500);

                setLongPressTimer(timer);
              }}
              onTouchStart={(e) => {
                if (e.touches[0]) {
                  // Set dragging state but don't prevent default scrolling yet
                  setIsDragging(true);

                  // Calculate offset from where user touched to the top-left corner of the button
                  const buttonRect = e.currentTarget.getBoundingClientRect();
                  setDragOffset({
                    x: e.touches[0].clientX - buttonRect.left,
                    y: e.touches[0].clientY - buttonRect.top,
                  });

                  // Start a timer for long press (500ms)
                  const timer = setTimeout(() => {
                    // Enter drag mode after long press
                    setIsDragMode(true);
                  }, 500);

                  setLongPressTimer(timer);
                }
              }}
            >
              {showNotificationsPanel ? 'Hide Notifications' : 'Show Notifications'}
              {!showNotificationsPanel && (
                <span className={`notification-badge ${orders.filter((order) => order.status === 'confirmed' && !viewedOrderIds.includes(order.id)).length > 0 ? 'has-notifications' : 'no-notifications'}`}>
                  {orders.filter((order) => order.status === 'confirmed' && !viewedOrderIds.includes(order.id)).length}
                </span>
              )}
            </button>
            {viewedOrderIds.length > 0 && (
              <button
                type="button"
                className="reset-notifications-btn"
                onClick={() => {
                  // Only reset if not dragging
                  if (!isDragging) {
                    setViewedOrderIds([]);
                    localStorage.removeItem('vendorViewedOrderIds');
                    toast.info('Notification history cleared');
                  }
                }}
                title="Reset notification history"
              >
                ↺
              </button>
            )}
          </div>
        </div>

        {/* Pickup Notifications Panel */}
        {showNotificationsPanel && (
        <Suspense fallback={<div className="loading-panel">Loading notifications...</div>}>
          <PickupNotificationsPanel
            viewedOrderIds={viewedOrderIds}
            onViewOrder={handleViewOrder}
            buttonPosition={buttonPosition}
            onButtonMouseDown={(e) => {
            // Prevent default to avoid text selection during drag
              e.preventDefault();

              // Start a timer for long press
              const timer = setTimeout(() => {
                setIsDragMode(true);
                // Visual feedback that we're in drag mode
                document.body.style.cursor = 'move';
              }, 500); // 500ms for long press

              setLongPressTimer(timer);

              // Calculate offset from the button's top-right corner
              const buttonRect = e.currentTarget.getBoundingClientRect();
              const offsetX = buttonRect.right - e.clientX;
              const offsetY = e.clientY - buttonRect.top;
              setDragOffset({ x: offsetX, y: offsetY });

              setIsDragging(true);
            }}
            onToggle={() => setShowNotificationsPanel(!showNotificationsPanel)}
            isDragMode={isDragMode}
            orders={orders.filter((order) => order.status === 'confirmed')}
          />
        </Suspense>
        )}

        {/* Mobile Notification Badge */}
        <MobileNotificationBadge
          onTogglePanel={() => setShowNotificationsPanel(!showNotificationsPanel)}
          showPanel={showNotificationsPanel}
          userRole="vendor"
          position={{ bottom: '80px', right: '20px' }}
        />

        {/* Dashboard Header with Wave Banner */}
        <div className="vendors-dashboard-header">
          <WaveBanner
            type="vendor"
            title="Vendor Dashboard"
            subtitle="Manage your {dynamicText} with ease"
            dynamicTexts={[
              'orders',
              'customers',
              'sales',
              'restaurant',
              'earnings',
              'deliveries',
            ]}
          />

          {/* Restaurant Status Button - positioned on the left */}
          <VendorAvailabilityToggle user={user} />

          {/* Help Button - positioned on the right */}
          <button
            type="button"
            className="tutorial-trigger-btn"
            onClick={() => setShowTutorial(true)}
            title="Show dashboard tutorial"
          >
            <HelpCircle size={20} />
            <span>Help</span>
          </button>
        </div>

        <div className="vendors-dashboard-container">

          {/* Dashboard Statistics */}
          {activeSection === 'dashboard' && (
          <Suspense fallback={<Loader />}>
            <VendorStatisticsSection />
          </Suspense>
          )}

          {/* Auto-refresh component */}
          <div className="dash">
            <AutoRefresh
              onRefresh={handleRefresh}
              interval={300000} // 5 minutes
              disabled={!vendorId}
            />
          </div>

          {/* Dashboard Content */}
          {(activeSection === 'not_received' || activeSection === 'received' || activeSection === 'pending' || activeSection === 'confirmed' || activeSection === 'processing' || activeSection === 'ready_for_pickup' || activeSection === 'delivered') && (
          <div className="vendors-dashboard-content">
            {status === 'loading' && filteredOrders.length === 0 && <div className="vendors-loading">Loading...</div>}
            {error && (
            <div className="vendors-status-notice vendors-error-notice">
              <h4>
                <AlertCircle size={18} style={{ verticalAlign: 'middle', marginRight: '0.5rem' }} />
                Error fetching orders
              </h4>
              <p>{error}</p>
            </div>
            )}
            {errorMessage && (
            <div className="vendors-status-notice vendors-error-notice">
              <h4>
                <AlertCircle size={18} style={{ verticalAlign: 'middle', marginRight: '0.5rem' }} />
                {errorMessage}
              </h4>
            </div>
            )}

            {activeSection === 'not_received' && (
            <div className="vendors-status-notice vendors-pending-notice">
              <h4>Not Received Orders</h4>
              <p>
                These are orders that have been placed by customers but
                haven&apos;t been delivered yet.
              </p>
            </div>
            )}

            {activeSection === 'pending' && (
            <div className="vendors-status-notice vendors-pending-notice">
              <h4>Pending Orders</h4>
              <p>
                These are orders that have not been paid for yet. Customers have added these items
                to their cart but haven&apos;t completed payment.
                You don&apos;t need to process these orders until they are confirmed.
              </p>
            </div>
            )}

            {activeSection === 'confirmed' && (
            <div className="vendors-status-notice vendors-confirmed-notice">
              <h4>Confirmed Orders</h4>
              <p>
                These orders have been paid for by customers and are ready to be processed.
                You should prioritize these orders and update their status to &quot;Processing&quot;
                once you start preparing them.
              </p>
            </div>
            )}

            {activeSection === 'ready_for_pickup' && (
            <div className="vendors-status-notice vendors-ready-notice">
              <h4>Ready for Pickup Orders</h4>
              <p>
                These orders are ready for pickup by riders. You can see the assigned rider&apos;s
                information for each order. If a rider is assigned, they have been notified that
                the order is ready for pickup.
              </p>
            </div>
            )}

            <h2 className="vendors-section-title">
              {activeSection === 'confirmed' && <Clipboard size={20} className="vendors-icon" />}
              {activeSection === 'processing' && <Clock size={20} className="vendors-icon" />}
              {activeSection === 'ready_for_pickup' && <Package size={20} className="vendors-icon" />}
              {activeSection === 'delivered' && <Truck size={20} className="vendors-icon" />}
              {activeSection === 'not_received' && <XCircle size={20} className="vendors-icon" />}
              {activeSection === 'received' && <CheckCircle size={20} className="vendors-icon" />}
              {activeSection === 'pending' && <ShoppingBag size={20} className="vendors-icon" />}
              {sections.find((s) => s.key === activeSection)?.label || 'Orders'}
            </h2>

            <div className="vendors-orders-container">
              {filteredOrders.length === 0 ? (
                <div className="vendors-empty-state">
                  <AlertCircle size={48} className="vendors-empty-state-icon" />
                  <p>No orders found in this category.</p>
                  <p>Orders will appear here when customers place them.</p>
                </div>
              ) : (
                <div className="vendors-orders-grid">
                  {filteredOrders.map((order) => {
                    // Access display_recipient for each individual order
                    const recipientInfo = order.display_recipient;

                    return (
                      <div key={order.id} id={`order-${order.id}`} className="vendors-order-card">
                        <div className="vendors-order-header">
                          <h3 className="vendors-order-id">
                            Order #
                            {order.id}
                          </h3>
                          <span className="vendors-order-time">
                            Ordered At:
                            {' '}
                            {convertToLocalTime(order.created_at)}
                          </span>
                        </div>

                        <div className="vendors-order-details">
                          <div className="vendors-detail-row">
                            <span className="vendors-detail-label">
                              <User size={16} style={{ verticalAlign: 'middle', marginRight: '0.5rem' }} />
                              Customer:
                            </span>
                            <span className="vendors-detail-value">{recipientInfo?.name || 'Loading...'}</span>
                          </div>

                          <div className="vendors-detail-row">
                            <span className="vendors-detail-label">
                              <MapPin size={16} style={{ verticalAlign: 'middle', marginRight: '0.5rem' }} />
                              Address:
                            </span>
                            <span className="vendors-detail-value vendors-full-address">{recipientInfo?.address}</span>
                          </div>

                          <div className="vendors-detail-row">
                            <span className="vendors-detail-label">
                              <Phone size={16} style={{ verticalAlign: 'middle', marginRight: '0.5rem' }} />
                              Phone:
                            </span>
                            <span className="vendors-detail-value">{recipientInfo?.phone || 'Not available'}</span>
                          </div>

                          <div className="vendors-detail-row">
                            <span className="vendors-detail-label">
                              <Tag size={16} style={{ verticalAlign: 'middle', marginRight: '0.5rem' }} />
                              Status:
                            </span>
                            <span className="vendors-detail-value">
                              <span className={`vendors-status-value ${order.status}`}>{order.status}</span>
                            </span>
                          </div>

                          <div className="vendors-detail-row">
                            <span className="vendors-detail-label">Update Status:</span>
                            <span className="vendors-detail-value">
                              <select
                                value={order.status ?? ''}
                                onChange={(e) => handleStatusUpdate(order.id, e.target.value)}
                                className="vendors-status-select"
                                disabled={order.status === 'received'}
                                aria-disabled={order.status === 'received'}
                                title={order.status === 'received' ? 'Order is locked (received) and cannot be changed' : 'Update order status'}
                              >
                                <option value="pending">Pending</option>
                                <option value="processing">Processing</option>
                                <option value="ready_for_pickup">Ready for Pickup</option>
                                <option value="cancelled">Cancelled</option>
                              </select>
                            </span>
                          </div>

                          {order.rider && (
                          <div className="vendors-rider-info">
                            <div className="vendors-detail-row">
                              <span className="vendors-detail-label">Rider:</span>
                              <span className="vendors-detail-value">
                                <span className="vendors-rider-name">{order.rider.name}</span>
                                <span className="vendors-rider-assigned-badge">Assigned</span>
                              </span>
                            </div>
                            <div className="vendors-detail-row">
                              <span className="vendors-detail-label">Phone:</span>
                              <span className="vendors-detail-value">{order.rider.phone || 'No phone available'}</span>
                            </div>
                          </div>
                          )}

                          {order.status === 'delivered' && (
                          <div className="vendors-detail-row">
                            <span className="vendors-detail-label">Payment:</span>
                            <span className="vendors-detail-value">
                              {orderPayouts[order.id] ? (
                                <span className="vendors-payment-received">
                                  Received: GH₵
                                  {' '}
                                  {orderPayouts[order.id].amount}
                                </span>
                              ) : (
                                <span className="vendors-payment-pending">Pending</span>
                              )}
                            </span>
                          </div>
                          )}

                          <div className="vendors-ordered-foods">
                            <h4>Ordered Items</h4>
                            {order.foods?.map((food, index) => (
                              <div key={food.id} className="vendors-food-item">
                                <img
                                  src={food.food_image_url}
                                  alt={food.name}
                                  className="vendors-food-img"
                                  onError={(e) => {
                                    e.target.onerror = null;
                                  }}
                                />
                                <div className="vendors-food-details">
                                  <span className="vendors-food-name">{food.name}</span>
                                  {food.description && (
                                  <div className="vendors-food-description">
                                    <p>
                                      {expandedDescriptions[food.id]
                                        ? food.description
                                        : `${food.description.substring(0, 80)}...`}
                                    </p>
                                    {food.description.length > 80 && (
                                    <button
                                      type="button"
                                      className="vendors-description-toggle"
                                      onClick={() => toggleDescription(food.id)}
                                    >
                                      {expandedDescriptions[food.id] ? 'See less' : 'See more'}
                                    </button>
                                    )}
                                  </div>
                                  )}
                                  <div className="vendors-food-meta">
                                    <span className="vendors-food-price">
                                      GH₵
                                      {food.price}
                                    </span>
                                    <span className="vendors-food-quantity">
                                      Qty:
                                      {order.quantities && index < order.quantities.length ? order.quantities[index] : 'N/A'}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
          )}

          {activeSection === 'addFood' && (
          <div className="vendors-dashboard-content">
            <h2 className="vendors-section-title">
              <ShoppingBag size={20} className="vendors-icon" />
              {' '}
              Add New Food Item
            </h2>
            <div className="vendors-add-food-container">
              {showAddFoodForm && (
              <Suspense fallback={<Loader />}>
                <AddFoodForm vendorId={vendorId} />
              </Suspense>
              )}
            </div>
          </div>
          )}
          {activeSection === 'payouts' && (
          <div className="vendors-dashboard-content">
            <h2 className="vendors-section-title">
              <DollarSign size={20} className="vendors-icon" />
              {' '}
              Your Payouts
            </h2>

            <div className="vendors-status-notice vendors-confirmed-notice">
              <h4>Payment Information</h4>
              <p>
                You receive only the food price portion of each order after delivery is confirmed.
                Service fees and delivery charges are deducted from the total price.
              </p>
            </div>

            {payoutsLoading && <div className="vendors-loading">Loading payouts...</div>}

            {!payoutsLoading && payouts.length === 0 && (
            <div className="vendors-empty-state">
              <AlertCircle size={48} className="vendors-empty-state-icon" />
              <p>No payouts found.</p>
              <p>Payouts will appear here when your orders are delivered.</p>
            </div>
            )}

            {!payoutsLoading && payouts.length > 0 && (
            <div className="vendors-payouts-container">
              <div className="vendors-payouts-table-wrapper">
                <table className="vendors-payouts-table">
                  <thead>
                    <tr>
                      <th>Order ID</th>
                      <th>Amount</th>
                      <th>Status</th>
                      <th>Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    {payouts.map((payout) => (
                      <tr key={payout.id} className={`vendors-payout-row ${payout.status}`}>
                        <td>{payout.order_id}</td>
                        <td>
                          GH₵
                          {payout.amount}
                        </td>
                        <td>
                          <span className={`vendors-payout-status vendors-status-${payout.status}`}>
                            {payout.status}
                          </span>
                        </td>
                        <td>{convertToLocalTime(payout.created_at)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            )}
          </div>
          )}

          {activeSection === 'foods' && (
          <div className="vendors-dashboard-content">
            <h2 className="vendors-section-title">
              <ShoppingBag size={20} className="vendors-icon" />
              {' '}
              Your Food Menu
            </h2>

            {!currentVendor?.is_available && (
            <div className="vendors-status-notice vendors-error-notice">
              <h4>
                <AlertCircle size={18} style={{ verticalAlign: 'middle', marginRight: '0.5rem' }} />
                Restaurant is Closed
              </h4>
              <p>Your restaurant is currently unavailable. Please set your restaurant status to "Open" to view and manage your menu.</p>
            </div>
            )}

            {foodsStatus === 'loading' && <div className="vendors-loading">Loading foods...</div>}

            {foodsError && (
            <div className="vendors-status-notice vendors-error-notice">
              <h4>
                <AlertCircle size={18} style={{ verticalAlign: 'middle', marginRight: '0.5rem' }} />
                Error fetching foods
              </h4>
              <p>{foodsError}</p>
            </div>
            )}

            {foods.length === 0 && foodsStatus !== 'loading' && !foodsError && (
            <div className="vendors-empty-state">
              <AlertCircle size={48} className="vendors-empty-state-icon" />
              <p>No foods available in your menu.</p>
              <p>Add food items from the &quot;Add Food&quot; section.</p>
            </div>
            )}

            {foods.length > 0 && (
            <div className="vendors-foods-table-container">
              <table className="vendors-foods-table">
                <thead>
                  <tr>
                    <th>Image</th>
                    <th>Food Name</th>
                    <th>Price</th>
                    <th>Description</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {foods.map((food) => (
                    <tr key={food.id}>
                      <td>
                        <img
                          src={food.food_image_url}
                          alt={food.name}
                          className="vendors-food-table-img"
                          onError={(e) => {
                            e.target.onerror = null;
                          }}
                        />
                      </td>
                      <td className="vendors-foods-name">{food.name}</td>
                      <td>
                        <span className="vendors-foods-price">{formatPriceRange(food)}</span>
                      </td>
                      <td className="vendors-foods-desc">{food.description}</td>
                      <td className="vendors-actions-cell">
                        <button type="button" className="vendors-action-btn vendors-edit-btn" onClick={() => openEditModal(food)}>
                          <Edit3 size={14} />
                          {' '}
                          Edit
                        </button>
                        <button type="button" className="vendors-action-btn vendors-delete-btn" onClick={() => promptDeleteFood(food)}>
                          <Trash2 size={14} />
                          {' '}
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            )}

          </div>
          )}
        </div>
      </div>

      {/* Edit Food Modal */}
      {editModalOpen && (
        <div className="vendors-modal-overlay" role="dialog" aria-modal="true">
          <div className="vendors-modal">
            <div className="vendors-modal-header">
              <h3>Edit Food</h3>
              <button type="button" className="vendors-modal-close" aria-label="Close" onClick={closeEditModal}>×</button>
            </div>

            <div className="vendors-modal-body">
              <div className="form-group">
                <label htmlFor="edit-name">
                  Name
                  <input id="edit-name" type="text" value={editName} onChange={(e) => setEditName(e.target.value)} />
                </label>
              </div>

              <div className="form-group">
                <label htmlFor="edit-description">
                  Description
                  <textarea id="edit-description" rows="3" value={editDescription} onChange={(e) => setEditDescription(e.target.value)} />
                </label>
              </div>

              <div className="form-group">
                <label htmlFor="priceMode">
                  Price Mode
                  <div className="radio-row">
                    <label htmlFor="priceModeSingle">
                      <input type="radio" name="priceMode" value="single" checked={editPriceMode === 'single'} onChange={() => setEditPriceMode('single')} />
                      Single Price
                    </label>
                    <label htmlFor="priceModeMultiple">
                      <input type="radio" name="priceMode" value="multiple" checked={editPriceMode === 'multiple'} onChange={() => setEditPriceMode('multiple')} />
                      Price Range
                    </label>
                  </div>
                </label>
              </div>

              {editPriceMode === 'single' ? (
                <div className="form-group">
                  <label htmlFor="edit-price">
                    Price (GH₵)
                    <input id="edit-price" type="number" min="0" step="0.01" value={editSinglePrice} onChange={(e) => setEditSinglePrice(e.target.value)} />
                  </label>
                </div>
              ) : (
                <div className="form-group">
                  <label htmlFor="edit-prices">
                    Prices (GH₵)
                    {editPrices.map((p, idx) => (
                      <div className="price-row" key={p.id}>
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          value={p}
                          onChange={(e) => {
                            const val = e.target.value;
                            setEditPrices((prev) => prev.map((x, i) => (i === idx ? val : x)));
                          }}
                        />
                        <button type="button" className="btn btn-sm" onClick={() => setEditPrices((prev) => prev.filter((_, i) => i !== idx))}>Remove</button>
                      </div>
                    ))}
                  </label>
                  <button type="button" className="btn btn-sm" onClick={() => setEditPrices((prev) => [...prev, ''])}>Add Price</button>
                </div>
              )}
            </div>

            <div className="vendors-modal-footer">
              <button type="button" className="btn btn-primary" onClick={handleSaveFoodEdits} disabled={isSaving}>
                {isSaving ? 'Saving...' : 'Save Changes'}
              </button>
              <button type="button" className="btn btn-secondary" onClick={closeEditModal} disabled={isSaving}>Cancel</button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteModalOpen && (
        <div className="vendors-modal-overlay" role="dialog" aria-modal="true">
          <div className="vendors-modal">
            <div className="vendors-modal-header">
              <h3>Delete Food</h3>
              <button type="button" className="vendors-modal-close" aria-label="Close" onClick={cancelDeleteFood}>×</button>
            </div>
            <div className="vendors-modal-body">
              <p>
                Are you sure you want to delete &quot;
                {pendingDeleteFood?.name}
                &quot;? This action cannot be undone.
              </p>
            </div>
            <div className="vendors-modal-footer">
              <button type="button" className="btn btn-secondary" onClick={cancelDeleteFood} disabled={isDeleting}>Cancel</button>
              <button type="button" className="btn btn-danger" onClick={confirmDeleteFood} disabled={isDeleting}>
                {isDeleting ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Dashboard Tutorial */}
      <DashboardTutorial
        isVisible={showTutorial}
        onClose={() => setShowTutorial(false)}
        onSkip={() => setShowTutorial(false)}
        tutorialSteps={tutorialSteps}
      />
    </div>
  );
};

export default VendorsDashboard;
