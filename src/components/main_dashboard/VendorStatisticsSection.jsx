import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  DollarSign, ShoppingBag, Users, Calendar,
} from 'lucide-react';
import StatisticsCard from './StatisticsCard';
import RevenueChart from './RevenueChart';
import { fetchVendorStatistics, selectVendorStatistics, selectVendorStatisticsStatus } from '../../redux/slice/vendersSlice';

const VendorStatisticsSection = () => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const vendorId = user?.id;
  const statistics = useSelector(selectVendorStatistics);
  const statisticsStatus = useSelector(selectVendorStatisticsStatus);

  useEffect(() => {
    if (vendorId) {
      dispatch(fetchVendorStatistics(vendorId));
    }
  }, [dispatch, vendorId]);

  // Format currency
  const formatCurrency = (value) => `GH₵${value?.toFixed(2) || '0.00'}`;

  // Prepare data for the revenue chart
  const prepareChartData = () => {
    if (!statistics || !statistics.revenue_trend) {
      return {
        labels: [],
        values: [],
      };
    }

    // Sort the data by month
    const sortedData = [...statistics.revenue_trend].sort((a, b) => {
      const monthA = new Date(`${a.month} 1, 2023`).getMonth();
      const monthB = new Date(`${b.month} 1, 2023`).getMonth();
      return monthA - monthB;
    });

    return {
      labels: sortedData.map((item) => item.month),
      values: sortedData.map((item) => item.revenue),
    };
  };

  if (statisticsStatus === 'loading') {
    return <div className="statistics-loading">Loading statistics...</div>;
  }

  if (statisticsStatus === 'failed' || !statistics) {
    return (
      <div className="statistics-error">
        <h3>Failed to load statistics</h3>
        <p>Please try again later or contact support if the problem persists.</p>
      </div>
    );
  }

  return (
    <div className="statistics-section">
      <h2 className="statistics-section-title">Dashboard Statistics</h2>

      <div className="statistics-cards-container">
        <StatisticsCard
          title="Today's Revenue"
          value={formatCurrency(statistics.daily_statistics?.revenue)}
          icon={<DollarSign size={20} />}
          color="#f8b400"
          period="Today"
          change={statistics.daily_statistics?.revenue_change}
          changeType={statistics.daily_statistics?.revenue_change_type || 'increase'}
        />

        <StatisticsCard
          title="Today's Orders"
          value={statistics.daily_statistics?.orders || 0}
          icon={<ShoppingBag size={20} />}
          color="#4caf50"
          period="Today"
          change={statistics.daily_statistics?.orders_change}
          changeType={statistics.daily_statistics?.orders_change_type || 'increase'}
        />

        <StatisticsCard
          title="Today's Customers"
          value={statistics.daily_statistics?.customers || 0}
          icon={<Users size={20} />}
          color="#2196f3"
          period="Today"
          change={statistics.daily_statistics?.customers_change}
          changeType={statistics.daily_statistics?.customers_change_type || 'increase'}
        />

        <StatisticsCard
          title="Monthly Revenue"
          value={formatCurrency(statistics.monthly_statistics?.revenue)}
          icon={<Calendar size={20} />}
          color="#9c27b0"
          period="This Month"
          change={statistics.monthly_statistics?.revenue_change}
          changeType={statistics.monthly_statistics?.revenue_change_type || 'increase'}
        />

        <StatisticsCard
          title="Monthly Orders"
          value={statistics.monthly_statistics?.orders || 0}
          icon={<ShoppingBag size={20} />}
          color="#4caf50"
          period="This Month"
          change={statistics.monthly_statistics?.orders}
          changeType={statistics.monthly_statistics?.orders_change_type || 'increase'}
        />

        <StatisticsCard
          title="Monthly Customers"
          value={statistics.monthly_statistics?.customers || 0}
          icon={<Users size={20} />}
          color="#2196f3"
          period="This Month"
          change={statistics.monthly_statistics?.customers}
          changeType={statistics.monthly_statistics?.customers_change_type || 'increase'}
        />
      </div>

      <div className="statistics-chart-container">
        <h3 className="statistics-chart-title">Revenue Trend</h3>
        <div className="revenue-chart-wrapper">
          <RevenueChart
            data={prepareChartData()}
            title="Revenue Trend (Last 6 Months)"
            period="Last 6 Months"
          />
        </div>
      </div>
    </div>
  );
};

export default VendorStatisticsSection;
