import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  DollarSign, Users, Truck, Calendar,
} from 'lucide-react';
import StatisticsCard from './StatisticsCard';
import RevenueChart from './RevenueChart';
import { fetchRiderStatistics, selectRiderStatistics, selectRiderStatisticsStatus } from '../../redux/slice/ridersSlice';

const RiderStatisticsSection = () => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const riderId = user?.id;
  const statistics = useSelector(selectRiderStatistics);
  const statisticsStatus = useSelector(selectRiderStatisticsStatus);

  useEffect(() => {
    if (riderId) {
      dispatch(fetchRiderStatistics(riderId));
    }
  }, [dispatch, riderId]);

  // Format currency
  const formatCurrency = (value) => `GH₵${value?.toFixed(2) || '0.00'}`;

  // Prepare data for the revenue chart
  const prepareChartData = () => {
    if (!statistics || !statistics.revenue_trend) {
      return {
        labels: [],
        values: [],
      };
    }

    // Sort the data by month
    const sortedData = [...statistics.revenue_trend].sort((a, b) => {
      const monthA = new Date(`${a.month} 1, 2023`).getMonth();
      const monthB = new Date(`${b.month} 1, 2023`).getMonth();
      return monthA - monthB;
    });

    return {
      labels: sortedData.map((item) => item.month),
      values: sortedData.map((item) => item.revenue),
    };
  };

  if (statisticsStatus === 'loading') {
    return <div className="statistics-loading">Loading statistics...</div>;
  }

  if (statisticsStatus === 'failed' || !statistics) {
    return (
      <div className="statistics-error">
        <h3>Failed to load statistics</h3>
        <p>Please try again later or contact support if the problem persists.</p>
      </div>
    );
  }

  return (
    <div className="statistics-section">
      <h2 className="statistics-section-title">Dashboard Statistics</h2>

      <div className="statistics-cards-container">
        <StatisticsCard
          title="Today's Earnings"
          value={formatCurrency(statistics.daily_statistics?.revenue)}
          icon={<DollarSign size={20} />}
          color="#f8b400"
          period="Today"
          change={statistics.daily_statistics?.revenue_change}
          changeType={statistics.daily_statistics?.revenue_change_type || 'increase'}
        />

        <StatisticsCard
          title="Today's Deliveries"
          value={statistics.daily_statistics?.deliveries || 0}
          icon={<Truck size={20} />}
          color="#4caf50"
          period="Today"
          change={statistics.daily_statistics?.deliveries_change}
          changeType={statistics.daily_statistics?.deliveries_change_type || 'increase'}
        />

        <StatisticsCard
          title="Today's Customers"
          value={statistics.daily_statistics?.customers || 0}
          icon={<Users size={20} />}
          color="#2196f3"
          period="Today"
        />

        <StatisticsCard
          title="Monthly Earnings"
          value={formatCurrency(statistics.monthly_statistics?.revenue)}
          icon={<Calendar size={20} />}
          color="#9c27b0"
          period="This Month"
          change={statistics.monthly_statistics?.revenue_change}
          changeType={statistics.monthly_statistics?.revenue_change_type || 'increase'}
        />

        <StatisticsCard
          title="Monthly Deliveries"
          value={statistics.monthly_statistics?.deliveries || 0}
          icon={<Truck size={20} />}
          color="#4caf50"
          period="This Month"
          change={statistics.monthly_statistics?.deliveries}
          changeType={statistics.monthly_statistics?.deliveries_change_type || 'increase'}
        />

        <StatisticsCard
          title="Monthly Customers"
          value={statistics.monthly_statistics?.customers || 0}
          icon={<Users size={20} />}
          color="#2196f3"
          period="This Month"
          change={statistics.monthly_statistics?.customers}
          changeType={statistics.monthly_statistics?.customers_change_type || 'increase'}
        />
      </div>

      <div className="statistics-chart-container">
        <h3 className="statistics-chart-title">Earnings Trend</h3>
        <div className="revenue-chart-wrapper">
          <RevenueChart
            data={prepareChartData()}
            title="Earnings Trend (Last 6 Months)"
            period="Last 6 Months"
          />
        </div>
      </div>
    </div>
  );
};

export default RiderStatisticsSection;
