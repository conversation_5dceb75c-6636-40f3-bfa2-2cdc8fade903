import React from 'react';
import PropTypes from 'prop-types';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import { Line } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
);

const RevenueChart = ({ data, title, period }) => {
  // Default options for the chart
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          font: {
            family: "'Poppins', sans-serif",
            size: 12,
          },
          color: '#666',
        },
      },
      title: {
        display: !!title,
        text: title,
        font: {
          family: "'Poppins', sans-serif",
          size: 16,
          weight: 'bold',
        },
        color: '#333',
        padding: {
          top: 10,
          bottom: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#333',
        bodyColor: '#666',
        borderColor: '#f8b400',
        borderWidth: 1,
        padding: 10,
        bodyFont: {
          family: "'Poppins', sans-serif",
        },
        titleFont: {
          family: "'Poppins', sans-serif",
          weight: 'bold',
        },
        callbacks: {
          label: (context) => `Revenue: GH₵${context.parsed.y.toFixed(2)}`,
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            family: "'Poppins', sans-serif",
            size: 12,
          },
          color: '#666',
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            family: "'Poppins', sans-serif",
            size: 12,
          },
          color: '#666',
          callback: (value) => `GH₵${value}`,
        },
      },
    },
  };

  // Prepare chart data
  const chartData = {
    labels: data.labels,
    datasets: [
      {
        label: `Revenue (${period})`,
        data: data.values,
        borderColor: '#f8b400',
        backgroundColor: 'rgba(248, 180, 0, 0.1)',
        borderWidth: 2,
        pointBackgroundColor: '#f8b400',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
        tension: 0.3,
        fill: true,
      },
    ],
  };

  return (
    <div className="revenue-chart-container">
      <Line options={options} data={chartData} />
    </div>
  );
};

RevenueChart.propTypes = {
  data: PropTypes.shape({
    labels: PropTypes.arrayOf(PropTypes.string).isRequired,
    values: PropTypes.arrayOf(PropTypes.number).isRequired,
  }).isRequired,
  title: PropTypes.string,
  period: PropTypes.string,
};

RevenueChart.defaultProps = {
  title: 'Revenue Trend',
  period: 'Last 6 Months',
};

export default RevenueChart;
