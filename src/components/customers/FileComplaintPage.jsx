import React, { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import axios from 'axios';
import {
  AlertTriangle,
  Send,
  User,
  Mail,
  Tag,
  FileText,
  ShoppingBag,
  Image,
  Clock,
  MessageSquare,
} from 'lucide-react';
import useLocalStorage from '../../hooks/useLocalStorage';
import ApiUrl from '../helper-functions/ApiUrl';

const FileComplaintPage = () => {
  const { user } = useSelector((state) => state.auth);
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    orderNumber: '',
    category: 'order',
    complaint: '',
    attachments: [],
  });

  const [formErrors, setFormErrors] = useState({});
  // Add loading state to handle API calls
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submittedComplaints, setSubmittedComplaints] = useState([]);
  // Add state to track if we've loaded user complaints
  const [loadedUserComplaints, setLoadedUserComplaints] = useState(false);

  // Use custom hook for auth data
  const [authData] = useLocalStorage('authData', null);

  // Function to fetch user's complaints from the backend - wrapped in useCallback
  const fetchUserComplaints = useCallback(async (userId) => {
    try {
      if (!authData) return;

      const response = await axios.get(
        `${ApiUrl}/customers/${userId}/complaints`,
        {
          headers: {
            Authorization: authData.token,
          },
        },
      );

      // Format the complaints for display
      const formattedComplaints = response.data.map((complaint) => ({
        id: complaint.id,
        name: complaint.name,
        category: complaint.category,
        complaint: complaint.complaint,
        orderNumber: complaint.order_number,
        status: complaint.status,
        date: new Date(complaint.created_at).toISOString().split('T')[0],
        response: complaint.admin_response,
      }));

      setSubmittedComplaints(formattedComplaints);
      setLoadedUserComplaints(true);
    } catch (error) {
      // Silent fail - don't show error toast as this is a background operation
      // Log error in development only
      if (process.env.NODE_ENV !== 'production') {
        // eslint-disable-next-line no-console
        console.error('Error fetching complaints:', error);
      }
    }
  }, [authData]);

  useEffect(() => {
    if (user) {
      setFormData((prev) => ({
        ...prev,
        name: user.name || '',
        email: user.email || '',
      }));

      // Load user's complaints if they're logged in and we haven't loaded them yet
      if (user.id && !loadedUserComplaints) {
        fetchUserComplaints(user.id);
      }
    }
  }, [user, loadedUserComplaints, fetchUserComplaints]);

  const categories = [
    { value: 'order', label: 'Order Issues', icon: <ShoppingBag size={16} /> },
    { value: 'delivery', label: 'Delivery Problems', icon: <Clock size={16} /> },
    { value: 'food', label: 'Food Quality', icon: <AlertTriangle size={16} /> },
    { value: 'app', label: 'App/Website Issues', icon: <FileText size={16} /> },
    { value: 'payment', label: 'Payment Problems', icon: <AlertTriangle size={16} /> },
    { value: 'other', label: 'Other', icon: <FileText size={16} /> },
  ];

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);

    if (files.length > 3) {
      toast.error('You can only upload up to 3 files');
      return;
    }

    const validFiles = files.filter((file) => {
      const isValidType = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'].includes(file.type);
      const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB

      if (!isValidType) {
        toast.error(`${file.name} is not a valid file type. Please upload images (JPEG, PNG) or PDF files.`);
      }

      if (!isValidSize) {
        toast.error(`${file.name} exceeds the 5MB size limit.`);
      }

      return isValidType && isValidSize;
    });

    setFormData((prev) => ({
      ...prev,
      attachments: validFiles,
    }));
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.orderNumber.trim()) {
      errors.orderNumber = 'Order number is required';
    }

    if (!formData.complaint.trim()) {
      errors.complaint = 'Complaint details are required';
    } else if (formData.complaint.trim().length < 10) {
      errors.complaint = 'Please provide more details about your complaint (at least 10 characters)';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Helper function to get category icon
  const getCategoryIcon = (category) => {
    const found = categories.find((cat) => cat.value === category);
    return found ? found.icon : <MessageSquare size={16} />;
  };

  // Helper function to get status badge class
  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'resolved':
        return 'status-resolved';
      case 'under_review':
        return 'status-review';
      case 'rejected':
        return 'status-rejected';
      case 'pending':
      default:
        return 'status-pending';
    }
  };

  // Updated handleSubmit function to integrate with backend API
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      // Set submitting state to true to show loading indicator
      setIsSubmitting(true);

      try {
        // Create form data for file uploads
        const formDataToSend = new FormData();

        // Append complaint data to FormData
        // Note: Backend expects snake_case keys, but frontend uses camelCase
        formDataToSend.append('complaint[name]', formData.name);
        formDataToSend.append('complaint[email]', formData.email);
        formDataToSend.append('complaint[order_number]', formData.orderNumber);
        formDataToSend.append('complaint[category]', formData.category);
        formDataToSend.append('complaint[complaint]', formData.complaint);

        // Add attachments if any
        formData.attachments.forEach((file) => {
          formDataToSend.append('attachments[]', file);
        });

        // Get auth token if user is logged in
        const headers = {};

        if (authData) {
          headers.Authorization = authData.token;
        }

        // Send data to backend
        const response = await axios.post(
          `${ApiUrl}/complaints`,
          formDataToSend,
          {
            headers: {
              ...headers,
              'Content-Type': 'multipart/form-data', // Important for file uploads
            },
          },
        );

        // Create a new complaint object for the UI with the response data
        const newComplaint = {
          id: response.data.id,
          name: formData.name,
          category: formData.category,
          complaint: formData.complaint,
          orderNumber: formData.orderNumber,
          status: response.data.status || 'pending',
          date: new Date().toISOString().split('T')[0],
        };

        // Update the UI with the new complaint at the top of the list
        setSubmittedComplaints([newComplaint, ...submittedComplaints]);

        // Show success message
        toast.success('Your complaint has been submitted successfully! Our team will review it shortly.');

        // Reset form
        setFormData({
          name: user?.name || '',
          email: user?.email || '',
          orderNumber: '',
          category: 'order',
          complaint: '',
          attachments: [],
        });

        // Success is already shown via toast notification
      } catch (error) {
        // Handle errors from the API

        // Extract error message from response if available
        const errorMessage = error.response?.data?.errors?.join(', ')
                            || error.response?.data?.error
                            || 'Failed to submit complaint. Please try again later.';

        toast.error(errorMessage);
      } finally {
        // Reset submitting state
        setIsSubmitting(false);
      }
    }
  };

  return (
    <div className="complaints-page">
      <div className="complaints-header">
        <h1>File a Complaint</h1>
        <p>
          We&apos;re sorry you&apos;ve experienced an issue with our service.
          Please provide details about your complaint, and our customer
          service team will address it as soon as possible.
        </p>
      </div>

      <div className="complaints-container">
        <div className="complaints-form-container">
          <h2>
            <AlertTriangle className="complaints-icon" />
            Report an Issue
          </h2>
          <form className="complaints-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="name">
                <div>
                  <User size={16} className="form-icon" />
                  Full Name
                  <span className="required">*</span>
                </div>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Your full name"
                />
                {formErrors.name && <p className="error-message">{formErrors.name}</p>}
              </label>
            </div>

            <div className="form-group">
              <label htmlFor="email">
                <div>
                  <Mail size={16} className="form-icon" />
                  Email Address
                  <span className="required">*</span>
                </div>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                />
                {formErrors.email && <p className="error-message">{formErrors.email}</p>}
              </label>
            </div>

            <div className="form-group">
              <label htmlFor="orderNumber">
                <div>
                  <ShoppingBag size={16} className="form-icon" />
                  Order Number
                  <span className="required">*</span>
                </div>
                <input
                  type="text"
                  id="orderNumber"
                  name="orderNumber"
                  value={formData.orderNumber}
                  onChange={handleChange}
                  placeholder="e.g., EF-2023-12-123"
                />
                {formErrors.orderNumber && <p className="error-message">{formErrors.orderNumber}</p>}
              </label>
            </div>

            <div className="form-group">
              <label htmlFor="category">
                <div>
                  <Tag size={16} className="form-icon" />
                  Issue Category
                  <span className="required">*</span>
                </div>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  className="form-select"
                >
                  {categories.map((category) => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </label>
            </div>

            <div className="form-group">
              <label htmlFor="complaint">
                <div>
                  <FileText size={16} className="form-icon" />
                  Complaint Details
                  <span className="required">*</span>
                </div>
                <textarea
                  id="complaint"
                  className="complaint-textarea"
                  name="complaint"
                  value={formData.complaint}
                  onChange={handleChange}
                  placeholder="Please describe your issue in detail..."
                  rows="5"
                />
                {formErrors.complaint && <p className="error-message">{formErrors.complaint}</p>}
              </label>
            </div>

            <div className="form-group">
              <label htmlFor="attachments">
                <div>
                  <Image size={16} className="form-icon" />
                  Attachments (Optional)
                </div>
                <div className="file-upload-container">
                  <input
                    type="file"
                    id="attachments"
                    name="attachments"
                    onChange={handleFileChange}
                    multiple
                    accept="image/jpeg,image/png,image/jpg,application/pdf"
                    className="file-input"
                  />
                  <div className="file-upload-info">
                    <p>Upload up to 3 files (images or PDFs, max 5MB each)</p>
                    {formData.attachments.length > 0 && (
                      <ul className="file-list">
                        {formData.attachments.map((file) => (
                          <li key={file.name} className="file-item">
                            <FileText size={14} />
                            <span>{file.name}</span>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              </label>
            </div>

            {/* Add disabled state when form is submitting */}
            <button
              type="submit"
              className="submit-button"
              disabled={isSubmitting}
            >
              <Send size={16} />
              {isSubmitting ? 'Submitting...' : 'Submit Complaint'}
            </button>
          </form>
        </div>

        <div className="submitted-complaints">
          <h2>Your Complaints</h2>

          {submittedComplaints.length === 0 ? (
            <p className="no-complaints">You haven&apos;t submitted any complaints yet. If you&apos;re experiencing an issue with our service, please fill out the form.</p>
          ) : (
            <div className="complaints-list">
              {submittedComplaints.map((item) => (
                <div key={item.id} className="complaint-card">
                  <div className="complaint-header">
                    <div className="complaint-category">
                      {getCategoryIcon(item.category)}
                      <span>{categories.find((cat) => cat.value === item.category)?.label || 'Other'}</span>
                    </div>
                    <div className={`complaint-status ${getStatusBadgeClass(item.status)}`}>
                      {item.status.replace('_', ' ')}
                    </div>
                  </div>

                  <div className="complaint-order-info">
                    <ShoppingBag size={14} />
                    <span>
                      Order:
                      {' '}
                      {item.orderNumber}
                    </span>
                  </div>

                  <div className="complaint-content">
                    <p>{item.complaint}</p>
                  </div>

                  {item.response && (
                    <div className="complaint-response">
                      <h4>Response from Support Team</h4>
                      <p>{item.response}</p>
                    </div>
                  )}

                  <div className="complaint-footer">
                    <div className="complaint-user">
                      <User size={14} />
                      <span>{item.name}</span>
                    </div>
                    <div className="complaint-date">
                      <Clock size={14} />
                      <span>{item.date}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileComplaintPage;
