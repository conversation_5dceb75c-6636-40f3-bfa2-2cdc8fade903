import {
  useEffect, useCallback, useState, useMemo,
} from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import {
  Calendar, MapPin, DollarSign, Truck, ShoppingBag,
  AlertTriangle, CheckCircle, Store, User, Phone, FileText,
  Package, X, Menu, BarChart, History, ChevronLeft, ChevronRight,
  ShoppingCart, HelpCircle, RotateCcw,
} from 'lucide-react';
import {
  selectOrders, selectOrdersStatus, fetchOrders, updateOrderStatus, confirmDelivery,
  fetchCustomerStatistics, selectStatistics, selectOrderHistory,
  selectStatisticsStatus, selectStatisticsError, selectPagination,
}
  from '../../redux/slice/ordersSlice';
import convertToLocalTime from '../helper-functions/convertToLocalTime';
import Loader from '../helper-functions/Loader';
import AutoRefresh from '../helper-functions/AutoRefresh';
import DashboardTutorial from '../helper-functions/DashboardTutorial';
import useLocalStorage from '../../hooks/useLocalStorage';

// Tutorial trigger button styles
const tutorialTriggerStyles = `
  .tutorial-trigger-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, #f8b400 0%, #e5a700 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(248, 180, 0, 0.3);
    z-index: 10;
  }

  .tutorial-trigger-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(248, 180, 0, 0.4);
  }

  .customer-dashboard-header {
    position: relative;
  }

  .dashboard-header {
    position: relative;
  }

  @media (max-width: 768px) {
    .tutorial-trigger-btn {
      top: 0.5rem;
      right: 0.5rem;
      padding: 0.4rem 0.8rem;
      font-size: 0.8rem;
    }
  }
`;

// Add styles to document
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = tutorialTriggerStyles;
  document.head.appendChild(styleSheet);
}

const CustomerOrders = () => {
  const dispatch = useDispatch();
  const ordersData = useSelector(selectOrders);
  const status = useSelector(selectOrdersStatus);
  const statistics = useSelector(selectStatistics);
  const orderHistory = useSelector(selectOrderHistory);
  const statisticsStatus = useSelector(selectStatisticsStatus);
  const statisticsError = useSelector(selectStatisticsError);
  const pagination = useSelector(selectPagination);

  const [confirmingOrderId, setConfirmingOrderId] = useState(null);
  const [activeSection, setActiveSection] = useState('all');
  // Initialize sidebar as expanded on desktop, collapsed on mobile
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(10);

  // Tutorial state
  const [showTutorial, setShowTutorial] = useLocalStorage('customerTutorialShown', false);

  // Tutorial steps for Customer Orders
  const tutorialSteps = [
    {
      title: 'Welcome to Your Order Dashboard!',
      description: "Let's take a quick tour to help you track and manage your food orders effectively.",
      icon: <ShoppingCart size={48} />,
      details: 'This tutorial will show you how to track orders, view order history, and manage your deliveries.',
    },
    {
      title: 'Navigation Sidebar',
      description: 'Use the sidebar to filter and view different types of orders.',
      targetSelector: '.dashboard-sidebar',
      icon: <Menu size={48} />,
      details: "• All Orders: View all your orders\n• Active Orders: Orders currently being processed\n• Delivered: Orders that have been delivered\n• Completed: Orders you've confirmed receipt\n• Cancelled: Orders that were cancelled\n• Order History: Detailed statistics and spending overview",
    },
    {
      title: 'Auto-Refresh Feature',
      description: 'Your dashboard automatically updates every 5 minutes to keep order status fresh.',
      targetSelector: '.auto-refresh-container',
      icon: <RotateCcw size={48} />,
      details: '• Data refreshes automatically in the background\n• Click the button to refresh manually\n• Progress ring shows time until next refresh\n• No page reloads or interruptions',
    },
    {
      title: 'Order Cards',
      description: 'Each order card shows detailed information about your order and delivery.',
      targetSelector: '.order-card',
      icon: <Package size={48} />,
      details: '• Order ID and date\n• Customer details and delivery address\n• Vendor information and location\n• Rider details (when assigned)\n• Order items and quantities\n• Total price and payment status',
    },
    {
      title: 'Order Status Tracking',
      description: 'Track your order from placement to delivery with real-time status updates.',
      targetSelector: '.order-status-badge',
      icon: <CheckCircle size={48} />,
      details: "• Pending: Order placed, waiting for confirmation\n• Confirmed: Order confirmed and being prepared\n• Processing: Vendor is preparing your order\n• Ready for Pickup: Order ready for rider pickup\n• Out for Delivery: Rider is delivering your order\n• Delivered: Order has been delivered\n• Received: You've confirmed receipt",
    },
    {
      title: 'Confirm Delivery',
      description: 'Confirm receipt of your order to complete the delivery process.',
      targetSelector: '.confirm-delivery-btn',
      icon: <CheckCircle size={48} />,
      details: "• Click 'Confirm Receipt' when you receive your order\n• This releases payment to the vendor and rider\n• You can also mark orders as 'Not Received' if needed\n• Confirmation helps maintain service quality",
    },
    {
      title: 'Order History & Statistics',
      description: 'View detailed statistics about your ordering history and spending.',
      targetSelector: '.order-history-container',
      icon: <History size={48} />,
      details: '• Total orders and completion rate\n• Monthly spending overview\n• Order statistics and trends\n• Detailed order history with dates\n• Track your food ordering patterns',
    },
    {
      title: "You're All Set!",
      description: 'You now know how to navigate your order dashboard effectively.',
      icon: <ShoppingCart size={48} />,
      details: '• Track your orders in real-time\n• Confirm deliveries promptly\n• View your order history and statistics\n• Stay updated with auto-refresh\n\nEnjoy your meals! 🍽️',
    },
  ];

  const { user } = useSelector((state) => state.auth);
  const customerId = user?.id;

  // Use useMemo to memoize orders to prevent dependency changes on every render
  const orders = useMemo(() => ordersData || [], [ordersData]);

  // Memoize order counts for sidebar badges
  const orderCounts = useMemo(() => {
    if (!orders || orders.length === 0) {
      return {
        all: 0,
        active: 0,
        delivered: 0,
        completed: 0,
        cancelled: 0,
      };
    }

    return {
      all: orders.length,
      active: orders.filter((order) => ['pending', 'confirmed', 'preparing', 'ready_for_pickup', 'out_for_delivery'].includes(order.status)).length,
      delivered: orders.filter((order) => order.status === 'delivered').length,
      completed: orders.filter((order) => order.status === 'received').length,
      cancelled: orders.filter((order) => ['cancelled', 'not_received'].includes(order.status)).length,
    };
  }, [orders]);

  // Memoize filtered orders based on active section
  const filteredOrders = useMemo(() => {
    if (orders.length === 0) {
      return [];
    }

    if (activeSection === 'all') {
      return orders;
    }
    if (activeSection === 'active') {
      return orders.filter((order) => ['pending', 'confirmed', 'preparing', 'ready_for_pickup', 'out_for_delivery'].includes(order.status));
    }
    if (activeSection === 'delivered') {
      return orders.filter((order) => order.status === 'delivered');
    }
    if (activeSection === 'completed') {
      return orders.filter((order) => order.status === 'received');
    }
    if (activeSection === 'cancelled') {
      return orders.filter((order) => ['cancelled', 'not_received'].includes(order.status));
    }

    return orders.filter((order) => order.status === activeSection);
  }, [orders, activeSection]);

  // Toggle sidebar
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // Handle responsive sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      setSidebarCollapsed(true);
    };

    // Set initial state
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    if (customerId) {
      dispatch(fetchOrders({ customerId }));
    }
  }, [dispatch, customerId]);

  // Fetch customer statistics when activeSection is 'history'
  useEffect(() => {
    if (customerId && activeSection === 'history') {
      dispatch(fetchCustomerStatistics({
        customerId,
        page: currentPage,
        perPage,
      }));
    }
  }, [dispatch, customerId, activeSection, currentPage, perPage]);

  // Sort orders by `created_at` in descending order
  const sortedFilteredOrders = useMemo(() => [...filteredOrders]
    .sort((a, b) => new Date(b.created_at) - new Date(a.created_at)),
  [filteredOrders]);

  const handleStatusUpdate = (orderId, newStatus) => {
    dispatch(updateOrderStatus({ orderId, status: newStatus, customerId }))
      .unwrap()
      .then(() => {
        dispatch(fetchOrders({ customerId }));
      })
      .catch(() => {
        toast.error('Error updating order status');
      });
  };

  const handleConfirmDelivery = (orderId) => {
    setConfirmingOrderId(orderId);

    toast.info(
      <div className="delivery-confirmation">
        <p>Are you sure you want to confirm receipt of this order?</p>
        <p>This will mark the order as received and release payment to the vendor and rider.</p>
        <div className="confirmation-buttons">
          <button
            type="button"
            onClick={() => {
              toast.dismiss();
              // Confirm delivery and release payment
              dispatch(confirmDelivery({ orderId, customerId }))
                .unwrap()
                .then(() => {
                  toast.success('Delivery confirmed! Payments have been initiated.');
                  // Automatically update order status to received
                  dispatch(updateOrderStatus({ orderId, status: 'received', customerId }))
                    .unwrap()
                    .then(() => {
                      dispatch(fetchOrders({ customerId }));
                    })
                    .catch(() => {
                      toast.error('Error updating order status to received');
                      dispatch(fetchOrders({ customerId }));
                    });
                })
                .catch((error) => {
                  toast.error(error?.error || 'Failed to confirm delivery');
                })
                .finally(() => {
                  setConfirmingOrderId(null);
                });
            }}
            className="confirm-btn"
          >
            Confirm
          </button>
          <button
            type="button"
            onClick={() => {
              toast.dismiss();
              // Mark as not received
              dispatch(updateOrderStatus({ orderId, status: 'not_received', customerId }))
                .unwrap()
                .then(() => {
                  toast.info('Order marked as not received.');
                  dispatch(fetchOrders({ customerId }));
                })
                .catch(() => {
                  toast.error('Error updating order status');
                })
                .finally(() => {
                  setConfirmingOrderId(null);
                });
            }}
            className="cancel-btn"
          >
            Cancel
          </button>
        </div>
      </div>,
      {
        autoClose: false,
        closeOnClick: false,
      },
    );
  };

  // Get status class for styling
  const getStatusClass = (status) => {
    switch (status) {
      case 'pending':
        return 'status-pending';
      case 'confirmed':
        return 'status-confirmed';
      case 'preparing':
        return 'status-preparing';
      case 'ready_for_pickup':
        return 'status-ready';
      case 'out_for_delivery':
        return 'status-out-delivery';
      case 'delivered':
        return 'status-delivered';
      case 'received':
        return 'status-received';
      case 'delayed':
        return 'status-delayed';
      case 'not_received':
        return 'status-not-received';
      case 'cancelled':
        return 'status-cancelled';
      default:
        return '';
    }
  };

  // Format status for display
  const formatStatus = (status) => {
    if (!status) return 'Unknown';
    return status
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      setCurrentPage(newPage);
    }
  };

  // Handle per page change
  const handlePerPageChange = (event) => {
    const newPerPage = parseInt(event.target.value, 10);
    setPerPage(newPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Auto-refresh function
  const handleRefresh = useCallback(async () => {
    if (customerId) {
      try {
        await dispatch(fetchOrders({ customerId })).unwrap();
        // Silent refresh - no toast messages
      } catch (error) {
        throw new Error('Refresh error:', error);
      }
    }
  }, [dispatch, customerId]);

  // Render loading state if status is loading
  if (status === 'loading') return <Loader />;

  // Show error toast if status is failed
  if (status === 'failed') {
    toast.error('Failed to load orders.');
  }

  // Show loading state if statistics are loading
  if (activeSection === 'history' && statisticsStatus === 'loading') {
    return <Loader />;
  }

  // Show error toast if statistics failed to load
  if (activeSection === 'history' && statisticsStatus === 'failed') {
    toast.error(`Failed to load order history: ${statisticsError}`);
  }

  return (
    <div className="dashboard-container">
      {/* Background Overlay */}
      <div
        className={`dashboard-overlay ${!sidebarCollapsed ? 'active' : ''}`}
        onClick={() => setSidebarCollapsed(true)}
        role="presentation"
      />

      {/* Sidebar Navigation */}
      <button
        type="button"
        className="dashboard-sidebar-toggle-mobile"
        onClick={toggleSidebar}
        aria-label="Toggle sidebar"
      >
        {sidebarCollapsed ? <Menu /> : <X />}
      </button>
      <div className={`dashboard-sidebar ${sidebarCollapsed ? 'collapsed' : ''}`}>
        <div className="dashboard-sidebar-header">
          <div className="dashboard-logo">
            <ShoppingBag className="dashboard-logo-icon" />
            <span>My Orders</span>
          </div>
          <button
            type="button"
            className="dashboard-sidebar-toggle"
            onClick={toggleSidebar}
            aria-label="Toggle sidebar"
          >
            {sidebarCollapsed ? <Menu /> : <X />}
          </button>
        </div>

        <nav className="dashboard-nav">
          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'all' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('all');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <BarChart className="dashboard-nav-icon" />
            <span>All Orders</span>
            {orderCounts.all > 0 && (
              <div className="dashboard-nav-badge">{orderCounts.all}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'active' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('active');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <Package className="dashboard-nav-icon" />
            <span>Active Orders</span>
            {orderCounts.active > 0 && (
              <div className="dashboard-nav-badge">
                {orderCounts.active}
              </div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'delivered' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('delivered');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <Truck className="dashboard-nav-icon" />
            <span>Delivered</span>
            {orderCounts.delivered > 0 && (
              <div className="dashboard-nav-badge">{orderCounts.delivered}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'completed' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('completed');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <CheckCircle className="dashboard-nav-icon" />
            <span>Completed</span>
            {orderCounts.completed > 0 && (
              <div className="dashboard-nav-badge">{orderCounts.completed}</div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'cancelled' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('cancelled');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <AlertTriangle className="dashboard-nav-icon" />
            <span>Cancelled</span>
            {orderCounts.cancelled > 0 && (
              <div className="dashboard-nav-badge">
                {orderCounts.cancelled}
              </div>
            )}
          </button>

          <button
            type="button"
            className={`dashboard-nav-item ${activeSection === 'history' ? 'active' : ''}`}
            onClick={() => {
              setActiveSection('history');
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <History className="dashboard-nav-icon" />
            <span>Order History</span>
          </button>
        </nav>
      </div>

      {/* Main Content Area */}
      <div className={`dashboard-main ${sidebarCollapsed ? 'expanded' : ''}`}>
        <div className="customer-orders-container">
          <div className="dashboard-header">
            <h2>
              {activeSection === 'all' && 'All Orders'}
              {activeSection === 'active' && 'Active Orders'}
              {activeSection === 'delivered' && 'Delivered Orders'}
              {activeSection === 'completed' && 'Completed Orders'}
              {activeSection === 'cancelled' && 'Cancelled Orders'}
              {activeSection === 'history' && 'Order History'}
            </h2>
            <p className="dashboard-subtitle">Track and manage your food orders</p>
            <button
              type="button"
              className="tutorial-trigger-btn"
              onClick={() => setShowTutorial(true)}
              title="Show dashboard tutorial"
            >
              <HelpCircle size={20} />
              <span>Help</span>
            </button>
          </div>

          {/* Auto-refresh component */}
          <div className="custom-dash">
            <AutoRefresh
              onRefresh={handleRefresh}
              interval={300000} // 5 minutes
              disabled={!customerId}
            />
          </div>

          {/* Order History Section */}
          {activeSection === 'history' && statistics && (
            <div className="order-history-container">
              <div className=".statistics-section-customer">
                <h3 className="statistics-title">Order Statistics</h3>
                <div className="statistics-grid">
                  <div key="stats-total-orders" className="stat-card">
                    <ShoppingCart className="stat-icon" />
                    <div className="stat-content">
                      <span className="stat-value">{statistics.total_orders}</span>
                      <span className="stat-label">Total Orders</span>
                    </div>
                  </div>
                  <div key="stats-completed-orders" className="stat-card">
                    <CheckCircle className="stat-icon" />
                    <div className="stat-content">
                      <span className="stat-value">{statistics.completed_orders}</span>
                      <span className="stat-label">Completed Orders</span>
                    </div>
                  </div>
                  <div key="stats-pending-orders" className="stat-card">
                    <Package className="stat-icon" />
                    <div className="stat-content">
                      <span className="stat-value">{statistics.pending_orders}</span>
                      <span className="stat-label">Pending Orders</span>
                    </div>
                  </div>
                  <div key="stats-in-progress-orders" className="stat-card">
                    <Truck className="stat-icon" />
                    <div className="stat-content">
                      <span className="stat-value">{statistics.in_progress_orders}</span>
                      <span className="stat-label">In Progress</span>
                    </div>
                  </div>
                </div>

                <div className="spending-section">
                  <h3 className="spending-title">Spending Overview</h3>
                  <div className="spending-grid">
                    <div key="spending-total-spent" className="spending-card">
                      <DollarSign className="spending-icon" />
                      <div className="spending-content">
                        <span className="spending-value">
                          GH₵
                          {statistics.total_spent.toFixed(2)}
                        </span>
                        <span className="spending-label">Total Spent</span>
                      </div>
                    </div>
                    <div key="spending-monthly-spent" className="spending-card">
                      <Calendar className="spending-icon" />
                      <div className="spending-content">
                        <span className="spending-value">
                          GH₵
                          {statistics.monthly_spent.toFixed(2)}
                        </span>
                        <span className="spending-label">This Month</span>
                      </div>
                    </div>
                    <div key="spending-monthly-orders" className="spending-card">
                      <History className="spending-icon" />
                      <div className="spending-content">
                        <span className="spending-value">{statistics.monthly_orders}</span>
                        <span className="spending-label">Orders This Month</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="order-history-section">
                <h3 className="history-title">Order History</h3>

                {orderHistory.orders.length === 0 ? (
                  <div className="empty-history">
                    <div className="empty-history-icon">📜</div>
                    <p>No order history found.</p>
                    <p className="empty-history-subtitle">
                      Your order history will appear here once you place orders.
                    </p>
                  </div>
                ) : (
                  <>
                    <div className="history-orders-grid">
                      {orderHistory.orders.map((order) => (
                        <div key={order.id} className={`order-card ${getStatusClass(order.status)}`}>
                          <div className="order-card-header">
                            <div className="order-id">
                              <span className="order-label">Order #</span>
                              <span className="order-number">{order.id}</span>
                            </div>
                            <div className={`order-status-badge ${getStatusClass(order.status)}`}>
                              {formatStatus(order.status)}
                            </div>
                          </div>

                          <div className="order-card-body">
                            <div className="order-info-section">
                              <div className="order-info-row">
                                <span className="info-label">
                                  <Calendar size={16} className="info-icon" />
                                  Order Date:
                                </span>
                                <span className="info-value">{convertToLocalTime(order.created_at)}</span>
                              </div>
                              <div className="order-info-row">
                                <span className="info-label">
                                  <DollarSign size={16} className="info-icon" />
                                  Total Amount:
                                </span>
                                <span className="info-value price">
                                  GH₵
                                  {' '}
                                  {order.total_price}
                                </span>
                              </div>

                              {order.vendors && order.vendors.length > 0 && (
                                <div className="order-info-row">
                                  <span className="info-label">
                                    <Store size={16} className="info-icon" />
                                    Vendor:
                                  </span>
                                  <span className="info-value vendor">
                                    {order.vendors[0].name}
                                  </span>
                                </div>
                              )}

                              {order.foods && order.foods.length > 0 && (
                                <div className="order-info-row">
                                  <span className="info-label">
                                    <ShoppingBag size={16} className="info-icon" />
                                    Items:
                                  </span>
                                  <span className="info-value items">
                                    {order.foods.length}
                                    {' '}
                                    item(s)
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="pagination-controls">
                      <div className="pagination-info">
                        Showing
                        {' '}
                        {orderHistory.orders.length}
                        {' '}
                        of
                        {' '}
                        {pagination.totalOrders}
                        {' '}
                        orders
                      </div>
                      <div className="pagination-buttons">
                        <button
                          type="button"
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 1}
                          className="pagination-button"
                        >
                          <ChevronLeft size={16} />
                          Previous
                        </button>
                        <span className="pagination-current">
                          Page
                          {' '}
                          {currentPage}
                          {' '}
                          of
                          {' '}
                          {pagination.totalPages}
                        </span>
                        <button
                          type="button"
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={currentPage === pagination.totalPages}
                          className="pagination-button"
                        >
                          Next
                          <ChevronRight size={16} />
                        </button>
                      </div>
                      <div className="per-page-control">
                        <label htmlFor="per-page-select">
                          Items per page:
                          <select
                            id="per-page-select"
                            value={perPage}
                            onChange={handlePerPageChange}
                            className="per-page-select"
                          >
                            <option value="5">5</option>
                            <option value="10">10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                          </select>
                        </label>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}

          {/* Empty Orders Message */}
          {activeSection !== 'history' && sortedFilteredOrders.length === 0 && (
            <div className="empty-orders">
              <div className="empty-orders-icon">🍽️</div>
              <p>No orders found in this category.</p>
              <p className="empty-orders-subtitle">
                {activeSection === 'all'
                  ? "You haven't placed any orders yet. Your order history will appear here once you place an order."
                  : `You don't have any ${activeSection} orders at the moment.`}
              </p>
            </div>
          )}

          {/* Regular Orders */}
          {activeSection !== 'history' && sortedFilteredOrders.length > 0 && (
            <div className="orders-grid">
              {sortedFilteredOrders.map((order) => {
                // Access display_recipient for each individual order
                const recipientInfo = order.display_recipient;

                return (
                  <div key={order.id} className={`order-card ${getStatusClass(order.status)}`}>
                    <div className="order-card-header">
                      <div className="order-id">
                        <span className="order-label">Order #</span>
                        <span className="order-number">{order.id}</span>
                      </div>
                      <div className={`order-status-badge ${getStatusClass(order.status)}`}>
                        {formatStatus(order.status)}
                      </div>
                    </div>

                    <div className="order-card-body">
                      <div className="order-info-section">
                        <h3 className="section-title">
                          <FileText size={18} className="section-icon" />
                          Order Details
                        </h3>
                        <div className="order-info-row">
                          <span className="info-label">
                            <Calendar size={16} className="info-icon" />
                            Order Date:
                          </span>
                          <span className="info-value">{convertToLocalTime(order.created_at)}</span>
                        </div>
                        <div className="order-info-row">
                          <span className="info-label">
                            <DollarSign size={16} className="info-icon" />
                            Total Amount:
                          </span>
                          <span className="info-value price">
                            GH₵
                            {' '}
                            {order.total_price}
                          </span>
                        </div>
                        <div className="order-info-row">
                          <span className="info-label">
                            <User size={16} className="info-icon" />
                            Recipient:
                          </span>
                          <span className="info-value address">{recipientInfo?.name || 'Loading...'}</span>
                        </div>
                        <div className="order-info-row">
                          <span className="info-label">
                            <Phone size={16} className="info-icon" />
                            Phone:
                          </span>
                          <span className="info-value address">{recipientInfo?.phone || 'Not available'}</span>
                        </div>
                        <div className="order-info-row">
                          <span className="info-label">
                            <MapPin size={16} className="info-icon" />
                            Delivery Address:
                          </span>
                          <span className="info-value address">{recipientInfo?.address}</span>
                        </div>
                        <div className="order-info-row">
                          <span className="info-label">
                            <AlertTriangle size={16} className="info-icon" />
                            Order Status:
                          </span>
                          <span className="info-value address">{order.status}</span>
                        </div>

                        <h3 className="section-title section-margin">
                          <Store size={18} className="section-icon" />
                          Vendor Details
                        </h3>
                        {/* Display vendor information from vendors array */}
                        {order.vendors && order.vendors.length > 0 && (
                        <div className="order-info-row">
                          <span className="info-label">
                            <Store size={16} className="info-icon" />
                            Vendor:
                          </span>
                          <span className="info-value vendor">
                            <div className="vendor-name">
                              {order.vendors[0].name}
                            </div>
                            {order.vendors[0].address && (
                            <div className="vendor-address">
                              <MapPin size={14} className="vendor-icon" />
                              {order.vendors[0].address}
                            </div>
                            )}
                          </span>
                        </div>
                        )}

                        <h3 className="section-title section-margin">
                          <Truck size={18} className="section-icon" />
                          Rider Details
                        </h3>
                        {order.rider && (
                        <div className="order-info-row">
                          <span className="info-label">
                            <Truck size={16} className="info-icon" />
                            Rider:
                          </span>
                          <span className="info-value rider">
                            <div className="rider-name">
                              <User size={14} className="rider-icon" />
                              {order.rider.name}
                            </div>
                            {order.rider.phone && (
                            <div className="rider-phone">
                              <Phone size={14} className="rider-icon" />
                              {order.rider.phone}
                            </div>
                            )}
                          </span>
                        </div>
                        )}
                      </div>

                      <div className="order-items-section">
                        <h3 className="section-title">
                          <ShoppingBag size={18} className="section-icon" />
                          Ordered Items
                        </h3>
                        <div className="order-items-list">
                          {order.foods && order.quantities ? (
                            order.foods.map((food, index) => (
                              <div key={food.id} className="food-item-card">
                                <div className="food-item-image">
                                  <img src={food.food_image_url} alt={food.name} />
                                </div>
                                <div className="food-item-details">
                                  <h4 className="food-item-name">{food.name}</h4>
                                  <p className="food-item-description">{food.description}</p>
                                  <div className="food-item-meta">
                                    <span className="food-item-price">
                                      GH₵
                                      {' '}
                                      {food.price}
                                    </span>
                                    <span className="food-item-quantity">
                                      <ShoppingBag size={14} className="quantity-icon" />
                                      Qty:
                                      {' '}
                                      {order.quantities[index] ?? 'N/A'}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            ))
                          ) : (
                            <p className="no-items-message">No food items available</p>
                          )}
                        </div>
                      </div>

                      <div className="order-actions-section">
                        <h3 className="section-title">
                          <AlertTriangle size={18} className="section-icon" />
                          Order Actions
                        </h3>
                        {order.status === 'delivered' && (
                        <div className="confirm-delivery-section">
                          <button
                            type="button"
                            onClick={() => handleConfirmDelivery(order.id)}
                            className="confirm-delivery-btn"
                            disabled={confirmingOrderId === order.id}
                          >
                            <CheckCircle size={16} className="button-icon" />
                            {confirmingOrderId === order.id ? 'Processing...' : 'Confirm Receipt'}
                          </button>
                          <p className="delivery-note">
                            Confirming receipt will release payment to the vendor and rider.
                          </p>
                        </div>
                        )}
                        {order.status !== 'received' && (
                        <div className="status-update-section">
                          <label htmlFor={`status-select-${order.id}`} className="status-label">
                            <AlertTriangle size={16} className="label-icon" />
                            Update Status:
                            <select
                              id={`status-select-${order.id}`}
                              value={order.status}
                              onChange={(e) => handleStatusUpdate(order.id, e.target.value)}
                              className="status-select"
                            >
                              <option value="not_received">Not Received</option>
                              <option value="received">Received</option>
                              <option value="delayed">Delayed</option>
                            </select>
                          </label>
                        </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Dashboard Tutorial */}
      <DashboardTutorial
        isVisible={showTutorial}
        onClose={() => setShowTutorial(false)}
        onSkip={() => setShowTutorial(false)}
        tutorialSteps={tutorialSteps}
      />
    </div>
  );
};

export default CustomerOrders;
