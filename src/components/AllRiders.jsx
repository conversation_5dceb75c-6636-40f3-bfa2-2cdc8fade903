import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchRiders, selectRiders, selectRidersStatus, selectRidersError,
} from '../redux/slice/ridersSlice';

const AllRiders = ({ selectedRider, onRiderChange, deliveryZone }) => {
  const dispatch = useDispatch();
  const riders = useSelector(selectRiders);
  const status = useSelector(selectRidersStatus);
  const error = useSelector(selectRidersError);

  useEffect(() => {
    dispatch(fetchRiders());
  }, [dispatch]);

  if (status === 'loading') {
    return <p className="loading-riders">Loading riders...</p>;
  }

  if (status === 'failed') {
    return (
      <p>
        Error loading riders:
        {error}
      </p>
    );
  }

  // Filter riders based on deliveryZone
  const availableRiders = deliveryZone === 'outside'
    ? riders.filter((rider) => rider.outside_dunkwa_price !== null)
    : riders;

  if (availableRiders.length === 0) {
    return <p className="no-outside-dunkwa">No riders available for this delivery zone.</p>;
  }

  const selectedRiderDetails = riders.find((rider) => rider.id === selectedRider);

  return (
    <div>
      <select
        id="rider-select"
        value={selectedRider || ''}
        onChange={(e) => onRiderChange(e.target.value)}
        aria-labelledby="rider-select"
      >
        <option value="">Select a Rider</option>
        {riders.map((rider) => (
          <option key={rider.id} value={rider.id}>
            {rider.name}
          </option>
        ))}
      </select>

      {selectedRiderDetails && (
        <div className="rider-details">
          {selectedRiderDetails.avatar_url && (
            <img
              src={selectedRiderDetails.avatar_url}
              alt={`${selectedRiderDetails.name}'s avatar`}
            />
          )}
          <div className="rider-sub-details">
            <h3>{selectedRiderDetails.name}</h3>
            <p>
              <strong>Status:</strong>
              {' '}
              {selectedRiderDetails.rider_status}
            </p>
            <p>
              <strong>Verification Status:</strong>
              {' '}
              {selectedRiderDetails.validation_status}
            </p>
            <p>
              <strong>Delivery Price:</strong>
              {' '}
              ₵
              {deliveryZone === 'within' ? selectedRiderDetails.within_dunkwa_price.toFixed(2) : selectedRiderDetails.outside_dunkwa_price.toFixed(2)}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

AllRiders.propTypes = {
  selectedRider: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  onRiderChange: PropTypes.func.isRequired,
  deliveryZone: PropTypes.string.isRequired,
};

export default AllRiders;
