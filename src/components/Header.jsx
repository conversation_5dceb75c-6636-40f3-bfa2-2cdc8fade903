import { useNavigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import {
  ShoppingCart, Sun, Moon, Sunrise, Utensils,
} from 'lucide-react';
import DesktopIconsMenu from './DesktopIconsMenu';

const Header = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useSelector((state) => state.auth);
  const carts = useSelector((state) => state.cart.items) || [];

  // Check if user is on a dashboard page
  const isDashboardPage = [
    '/riders-dashboard',
    '/vendors-dashboard',
    '/customer-orders',
    '/admin-dashboard'
  ].includes(location.pathname);

  // Determine the greeting based on the current time
  const getGreeting = () => {
    const hour = new Date().getUTCHours();
    if (hour >= 5 && hour < 12) {
      return {
        text: 'Good Morning',
        icon: <Sunrise size={16} />,
        color: '#ff9800',
      };
    }
    if (hour >= 12 && hour < 18) {
      return {
        text: 'Good Afternoon',
        icon: <Sun size={16} />,
        color: '#f8b400',
      };
    }
    return {
      text: 'Good Evening',
      icon: <Moon size={16} />,
      color: '#6a5acd',
    };
  };

  // Get first name or username
  const getShortName = () => {
    if (!user || !user.name) return '';
    return user.name.split(' ')[0];
  };

  const totalItemsInCart = carts.reduce((total, item) => total + item.quantity, 0);

  const handleCartClick = () => {
    navigate('/cart');
  };

  // Get greeting and short name
  const greeting = getGreeting();
  const shortName = getShortName();

  return (
    <header className="header">
      <div className="logo-container">
        {!isDashboardPage && (
          <h1 className="logo">
            <Utensils size={24} className="logo-icon" />
            Ease
            <span className="logo-accent">Food</span>
          </h1>
        )}
      </div>

      {user && (
        <div className="greeting-container">
          <div
            className="greeting-content"
            style={{
              backgroundColor: `${greeting.color}15`,
              borderColor: `${greeting.color}30`,
              boxShadow: `0 0.25rem 1rem ${greeting.color}20`,
            }}
          >
            <div className="greeting-text">
              <div className="greeting-stack">
                <span className="greeting-time" style={{ color: greeting.color }}>
                  {greeting.icon}
                  <span>{greeting.text}</span>
                </span>
                {shortName && (
                  <span className="user-name" style={{ color: `${greeting.color === '#6a5acd' ? '#555' : '#333'}` }}>
                    {shortName}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Desktop Icons Menu */}
      <div className="desktop-menu-container">
        <DesktopIconsMenu />
      </div>

      {/* Cart Button - Now positioned last */}
      <button
        type="button"
        onClick={handleCartClick}
        aria-label="Toggle cart visibility"
        className="cart-button"
      >
        <ShoppingCart size={20} className="cart-icon" />
        {totalItemsInCart > 0 && (
          <span className="cart-badge">{totalItemsInCart}</span>
        )}
      </button>
    </header>
  );
};

export default Header;
