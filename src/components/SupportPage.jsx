import React, { useState } from 'react';
import {
  HelpCircle,
  Search,
  ChevronDown,
  ChevronUp,
  ShoppingBag,
  CreditCard,
  Truck,
  User,
  Settings,
  AlertTriangle,
  Phone,
  MessageSquare,
  FileText,
  Home,
  Menu,
  MapPin,
  Clock,
} from 'lucide-react';
import { Link } from 'react-router-dom';

const SupportPage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [expandedFaqs, setExpandedFaqs] = useState({});

  // Support categories
  const categories = [
    { id: 'all', name: 'All Topics', icon: <HelpCircle size={20} /> },
    { id: 'ordering', name: 'Ordering', icon: <ShoppingBag size={20} /> },
    { id: 'payment', name: 'Payment', icon: <CreditCard size={20} /> },
    { id: 'delivery', name: 'Delivery', icon: <Truck size={20} /> },
    { id: 'account', name: 'Account', icon: <User size={20} /> },
    { id: 'app', name: 'App Usage', icon: <Settings size={20} /> },
    { id: 'issues', name: 'Common Issues', icon: <AlertTriangle size={20} /> },
  ];

  // FAQ data
  const faqs = [
    {
      id: 1,
      question: 'How do I place an order?',
      answer: 'To place an order, browse through available vendors on the home page, select a vendor to view their menu, add items to your cart, and proceed to checkout. You can review your order, select delivery options, and complete payment to finalize your order.',
      category: 'ordering',
      keywords: ['order', 'place order', 'checkout', 'buy', 'purchase'],
    },
    {
      id: 2,
      question: 'What payment methods are accepted?',
      answer: 'We accept mobile money payments (MTN Mobile Money, Vodafone Cash, AirtelTigo Money) and cash on delivery. You can select your preferred payment method during checkout.',
      category: 'payment',
      keywords: ['payment', 'pay', 'mobile money', 'cash', 'mtn', 'vodafone'],
    },
    {
      id: 3,
      question: 'How long does delivery take?',
      answer: 'Delivery times vary based on your location, the vendor\'s location, and current demand. Typically, deliveries within Dunkwa take 30-45 minutes, while deliveries outside Dunkwa may take 45-60 minutes. You can track your order status in real-time through the app.',
      category: 'delivery',
      keywords: ['delivery time', 'how long', 'wait time', 'track order'],
    },
    {
      id: 4,
      question: 'How do I create an account?',
      answer: 'To create an account, click on the "Login" button and then select "Sign up". Fill in your details including name, phone number, email, and password. You can also select your role (customer, vendor, or rider) during signup.',
      category: 'account',
      keywords: ['account', 'sign up', 'register', 'create account'],
    },
    {
      id: 5,
      question: 'How do I reset my password?',
      answer: 'If you\'ve forgotten your password, go to the login page and click on "Forgot Password". Enter your email address, and we\'ll send you instructions to reset your password.',
      category: 'account',
      keywords: ['password', 'reset password', 'forgot password', 'login issues'],
    },
    {
      id: 6,
      question: 'How do I navigate the app?',
      answer: 'The bottom navigation bar provides access to key features: Home (browse vendors), Orders (view your orders), Profile (manage your account), Contact (reach customer support), and Suggestions (share your ideas). You can also use the search bar to find specific vendors or food items.',
      category: 'app',
      keywords: ['navigate', 'use app', 'find', 'search', 'menu'],
    },
    {
      id: 7,
      question: 'What do I do if my order is late?',
      answer: 'If your order is taking longer than expected, you can check its status in the Orders section. If there\'s a significant delay, you can contact the rider directly through the app or reach out to our customer support team at **********.',
      category: 'issues',
      keywords: ['late', 'delay', 'waiting', 'order status'],
    },
    {
      id: 8,
      question: 'How do I report an issue with my order?',
      answer: 'If you experience any issues with your order (missing items, incorrect order, quality concerns), you can file a complaint through the app. Go to the Orders section, find the specific order, and select "File a Complaint". Provide details about the issue, and our team will address it promptly.',
      category: 'issues',
      keywords: ['issue', 'problem', 'complaint', 'wrong order', 'missing items'],
    },
    {
      id: 9,
      question: 'How are delivery fees calculated?',
      answer: 'Delivery fees are based on the distance between the vendor and your delivery location. Riders set their base fees, and we add a small service fee (₵2 for deliveries within Dunkwa, ₵4 for deliveries outside Dunkwa). The total delivery fee is displayed during checkout before you confirm your order.',
      category: 'delivery',
      keywords: ['delivery fee', 'cost', 'charges', 'price'],
    },
    {
      id: 10,
      question: 'Can I schedule an order for later?',
      answer: 'Currently, we don\'t support scheduled orders. All orders are processed for immediate preparation and delivery. However, you can check vendor operating hours to plan your order accordingly.',
      category: 'ordering',
      keywords: ['schedule', 'later', 'advance order', 'future'],
    },
    {
      id: 11,
      question: 'How do I become a vendor on EaseFood?',
      answer: 'To become a vendor, sign up through the app and select "Vendor" as your role. You\'ll need to provide additional information such as your business name, operating hours, location, and mobile money details for receiving payments. Our team will review your application and contact you to complete the onboarding process.',
      category: 'account',
      keywords: ['vendor', 'sell', 'restaurant', 'become vendor'],
    },
    {
      id: 12,
      question: 'How do I become a rider on EaseFood?',
      answer: 'To become a rider, sign up through the app and select "Rider" as your role. You\'ll need to provide additional information such as your Ghana Card number, delivery pricing, and mobile money details for receiving payments. Our team will review your application and contact you to complete the onboarding process.',
      category: 'account',
      keywords: ['rider', 'deliver', 'become rider', 'delivery person'],
    },
    {
      id: 13,
      question: 'What happens if a vendor is closed?',
      answer: 'Vendors set their operating hours in the app. If a vendor is currently closed, they will appear in the list but will be marked as "Closed". You won\'t be able to place orders from closed vendors. You can check the vendor\'s operating hours to know when they\'ll be open again.',
      category: 'ordering',
      keywords: ['closed', 'vendor closed', 'operating hours', 'unavailable'],
    },
    {
      id: 14,
      question: 'How do I track my order?',
      answer: 'You can track your order in real-time through the Orders section of the app. Once a rider accepts your order, you\'ll be able to see their location and estimated arrival time. You can also contact the rider directly through the app if needed.',
      category: 'delivery',
      keywords: ['track', 'location', 'where is my order', 'status'],
    },
    {
      id: 15,
      question: 'How do I contact customer support?',
      answer: 'You can contact our customer support team through the Contact page in the app. For general inquiries, call 0240095360 or 0543020846. For technical support, call **********. You can also send us a message through the contact form.',
      category: 'issues',
      keywords: ['support', 'help', 'contact', 'assistance'],
    },
  ];

  // Quick links data
  const quickLinks = [
    {
      title: 'Place an Order',
      description: 'Browse vendors and order delicious food',
      icon: <ShoppingBag size={24} />,
      path: '/',
      color: '#f8b400',
    },
    {
      title: 'View Your Orders',
      description: 'Track and manage your current and past orders',
      icon: <Clock size={24} />,
      path: '/customer-orders',
      color: '#4caf50',
    },
    {
      title: 'File a Complaint',
      description: 'Report an issue with your order',
      icon: <FileText size={24} />,
      path: '/customer-file-complain',
      color: '#f44336',
    },
    {
      title: 'Contact Us',
      description: 'Get in touch with our support team',
      icon: <Phone size={24} />,
      path: '/contact',
      color: '#2196f3',
    },
    {
      title: 'Share Suggestions',
      description: 'Help us improve with your ideas',
      icon: <MessageSquare size={24} />,
      path: '/suggestions',
      color: '#9c27b0',
    },
    {
      title: 'Manage Profile',
      description: 'Update your account information',
      icon: <User size={24} />,
      path: '/profile',
      color: '#ff9800',
    },
  ];

  // Navigation guides
  const navigationGuides = [
    {
      title: 'Home Dashboard',
      icon: <Home size={20} />,
      steps: [
        'Access the home dashboard by clicking the Home icon in the bottom navigation',
        'Browse through featured vendors and promotions',
        'Use the search bar to find specific vendors or food items',
        'Click on a vendor card to view their menu',
      ],
    },
    {
      title: 'Finding Food',
      icon: <Menu size={20} />,
      steps: [
        'Select a vendor from the home dashboard',
        'Browse through their menu categories',
        'Click on a food item to view details',
        'Use the "Add to Cart" button to select items',
        'Adjust quantities in the cart before checkout',
      ],
    },
    {
      title: 'Delivery Options',
      icon: <MapPin size={20} />,
      steps: [
        'During checkout, you can select your delivery address',
        'Choose from saved addresses or add a new one',
        'The app will show available riders in your area',
        'Delivery fees are calculated based on distance',
        'You can track your delivery in real-time after order confirmation',
      ],
    },
  ];

  // Toggle FAQ expansion
  const toggleFaq = (id) => {
    setExpandedFaqs((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Filter FAQs based on search and category
  const filteredFaqs = faqs.filter((faq) => {
    const matchesSearch = searchQuery === ''
      || faq.question.toLowerCase().includes(searchQuery.toLowerCase())
      || faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      || faq.keywords.some((keyword) => keyword.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesCategory = activeCategory === 'all' || faq.category === activeCategory;

    return matchesSearch && matchesCategory;
  });

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Handle category selection
  const handleCategoryChange = (categoryId) => {
    setActiveCategory(categoryId);
  };

  // Clear search
  const clearSearch = () => {
    setSearchQuery('');
  };

  return (
    <div className="support-page">
      <div className="support-header">
        <h1>
          <HelpCircle className="support-icon" />
          Help & Support
        </h1>
        <p>Find answers to common questions and learn how to use EaseFood</p>

        <div className="support-search">
          <Search className="search-icon" />
          <input
            type="text"
            placeholder="Search for help topics..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="search-input"
          />
          {searchQuery && (
            <button className="clear-search" type="button" onClick={clearSearch}>
              ×
            </button>
          )}
        </div>
      </div>

      <div className="support-content">
        <div className="support-sidebar">
          <div className="categories-container">
            <h3>Categories</h3>
            <ul className="categories-list">
              {categories.map((category) => (
                <li key={category.id}>
                  <button
                    type="button"
                    className={`category-item ${activeCategory === category.id ? 'active' : ''}`}
                    onClick={() => handleCategoryChange(category.id)}
                  >
                    {category.icon}
                    <span>{category.name}</span>
                  </button>
                </li>
              ))}
            </ul>
          </div>

          <div className="quick-links-container">
            <h3>Quick Links</h3>
            <div className="quick-links">
              {quickLinks.map((link) => (
                <Link to={link.path} key={link.title} className="quick-link-card">
                  <div className="quick-link-icon" style={{ backgroundColor: link.color }}>
                    {link.icon}
                  </div>
                  <div className="quick-link-content">
                    <h4>{link.title}</h4>
                    <p>{link.description}</p>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>

        <div className="support-main">
          <div className="faqs-container">
            <h2>Frequently Asked Questions</h2>

            {filteredFaqs.length === 0 ? (
              <div className="no-results">
                <AlertTriangle size={24} />
                <p>
                  No results found for &quot;
                  {searchQuery}
                  &quot;
                </p>
                <button type="button" className="reset-search" onClick={clearSearch}>
                  Clear Search
                </button>
              </div>
            ) : (
              <div className="faqs-list">
                {filteredFaqs.map((faq) => (
                  <div
                    key={faq.id}
                    className={`faq-item ${expandedFaqs[faq.id] ? 'expanded' : ''}`}
                  >
                    <button
                      type="button"
                      className="faq-question"
                      onClick={() => toggleFaq(faq.id)}
                    >
                      <h3>{faq.question}</h3>
                      {expandedFaqs[faq.id] ? (
                        <ChevronUp className="faq-icon" />
                      ) : (
                        <ChevronDown className="faq-icon" />
                      )}
                    </button>

                    {expandedFaqs[faq.id] && (
                      <div className="faq-answer">
                        <p>{faq.answer}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="navigation-guides">
            <h2>Navigation Guides</h2>
            <div className="guides-list">
              {navigationGuides.map((guide) => (
                <div key={guide.title} className="guide-card">
                  <div className="guide-header">
                    {guide.icon}
                    <h3>{guide.title}</h3>
                  </div>
                  <ol className="guide-steps">
                    {guide.steps.map((step) => (
                      <li key={step.substring(0, 20)}>{step}</li>
                    ))}
                  </ol>
                </div>
              ))}
            </div>
          </div>

          <div className="contact-support">
            <h2>Still Need Help?</h2>
            <p>
              If you could not find what you are looking
              for, our support team is here to help.
            </p>
            <div className="support-contact-options">
              <div className="support-contact-card">
                <Phone size={24} className="contact-icon" />
                <div className="contact-details">
                  <h4>General Inquiries</h4>
                  <p>0240095360 / 0543020846</p>
                </div>
              </div>
              <div className="support-contact-card">
                <Settings size={24} className="contact-icon" />
                <div className="contact-details">
                  <h4>Technical Support</h4>
                  <p>**********</p>
                </div>
              </div>
              <Link to="/contact" className="contact-button">
                Contact Us
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupportPage;
