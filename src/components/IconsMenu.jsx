import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  Home,
  LogIn,
  UserPlus,
  Phone,
  Menu,
  LayoutDashboard,
  ShoppingBag,
  LogOut,
  User,
  FileText,
  HelpCircle,
  MessageSquare,
} from 'lucide-react';
import RoleSelectionPopup from './helper-functions/RoleSelectionPopup';
import { logout } from '../redux/slice/authSlice';
import { fetchOrders } from '../redux/slice/ordersSlice';

const IconsMenu = () => {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [formType, setFormType] = useState('');
  const [showMenu, setShowMenu] = useState(false);

  const location = useLocation();
  const currentPath = location.pathname;

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { isAuthenticated, role, user } = useSelector((state) => state.auth);
  // Define role as a string type
  const userRole = role || '';
  const customerId = user?.id;

  const routes = {
    home: '/',
    login: '/login',
    signup: '/signup',
    Profile: '/profile',
    customerOrders: '/customer-orders',
    fileComplain: '/customer-file-complain',
    ridersDashboard: '/riders-dashboard',
    vendorsDashboard: '/vendors-dashboard',
    adminDashboard: '/admin-dashboard',
    suggestions: '/suggestions',
    support: '/support',
    contact: '/contact',
  };

  useEffect(() => {
    setShowMenu(false);
  }, [isAuthenticated]);

  const handleNavigation = (path, state = null) => navigate(path, { state });

  const handleSelectRole = (selectedRole, type) => {
    setFormType(type);
    setIsPopupOpen(false);
    handleNavigation(type === 'login' ? routes.login : routes.signup, { role: selectedRole });
  };

  const toggleMenu = () => setShowMenu((prev) => !prev);

  const handleMenuAction = (action) => {
    const actions = {
      profile: () => handleNavigation(routes.Profile),
      orders: () => {
        if (customerId) {
          dispatch(fetchOrders({ customerId }));
        }
        handleNavigation(routes.customerOrders);
      },
      complain: () => handleNavigation(routes.fileComplain),
      support: () => handleNavigation(routes.support),
      suggestions: () => handleNavigation(routes.suggestions),
    };
    actions[action]?.();
    setShowMenu(false);
  };

  const handleLogout = () => {
    dispatch(logout(userRole));
    handleNavigation(routes.home);
  };

  // Helper function to determine dashboard route based on role
  const getDashboardRoute = (userRole) => {
    if (userRole === 'admin') {
      return routes.adminDashboard;
    }
    if (userRole === 'rider') {
      return routes.ridersDashboard;
    }
    return routes.vendorsDashboard;
  };

  const renderMenuDropdown = () => (
    <ul className="menu-dropdown">
      <li>
        <button type="button" onClick={() => handleMenuAction('profile')}>
          <User size={18} style={{ marginRight: '0.5rem' }} />
          Profile
        </button>
      </li>
      <li>
        <button type="button" onClick={() => handleMenuAction('complain')}>
          <FileText size={18} style={{ marginRight: '0.5rem' }} />
          File Complaint
        </button>
      </li>
      <li>
        <button type="button" onClick={() => handleMenuAction('support')}>
          <HelpCircle size={18} style={{ marginRight: '0.5rem' }} />
          Support
        </button>
      </li>
      <li>
        <button type="button" onClick={() => handleMenuAction('suggestions')}>
          <MessageSquare size={18} style={{ marginRight: '0.5rem' }} />
          Suggestions
        </button>
      </li>
      <li>
        <button type="button" onClick={handleLogout}>
          <LogOut size={18} style={{ marginRight: '0.5rem' }} />
          Logout
        </button>
      </li>
    </ul>
  );

  const renderIcon = (key, Icon, label, onClick, path = null) => {
    const isActive = path && currentPath === path;

    return (
      <li key={key}>
        <button
          type="button"
          className={`icon-container ${isActive ? 'active' : ''}`}
          onClick={onClick}
        >
          <Icon size={24} color={isActive ? '#f8b400' : '#555'} />
          <p>{label}</p>
        </button>
      </li>
    );
  };

  const renderMenuItems = () => {
    const items = [
      renderIcon('home', Home, 'Home', () => handleNavigation(routes.home), routes.home),
    ];

    if (isAuthenticated) {
      items.push(
        userRole === 'customer' && renderIcon('orders', ShoppingBag, 'Orders',
          () => handleNavigation(routes.customerOrders), routes.customerOrders),
        userRole !== 'customer' && renderIcon('dashboard', LayoutDashboard, 'Dashboard',
          () => {
            // Use the helper function to determine the dashboard route
            handleNavigation(getDashboardRoute(userRole));
          },
          getDashboardRoute(userRole)),
        renderIcon('menu', Menu, 'Menu', toggleMenu),
        showMenu && <li key="dropdown">{renderMenuDropdown()}</li>,
      );
    } else {
      items.push(
        renderIcon('login', LogIn, 'Login', () => {
          setFormType('login');
          setIsPopupOpen(true);
        }),
        renderIcon('signup', UserPlus, 'Sign Up', () => {
          setFormType('signup');
          setIsPopupOpen(true);
        }),
      );
    }

    items.push(
      renderIcon('contact', Phone, 'Contact', () => handleNavigation(routes.contact), routes.contact),
    );

    return items.filter(Boolean);
  };

  return (
    <section className="icons-menu">
      <ul>
        {renderMenuItems()}
      </ul>

      {isPopupOpen && (
        <RoleSelectionPopup
          onClose={() => {
            setIsPopupOpen(false);
            setFormType('');
          }}
          onSelect={(selectedRole) => handleSelectRole(selectedRole, formType)}
          isLogin={formType === 'login'}
        />
      )}
    </section>
  );
};

export default IconsMenu;
