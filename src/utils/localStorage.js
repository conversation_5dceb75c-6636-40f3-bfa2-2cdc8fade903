/**
 * Get an item from localStorage
 * @param {string} key - The key to retrieve from localStorage
 * @param {any} defaultValue - The default value to return if the key doesn't exist
 * @returns {any} The parsed value from localStorage or the default value
 */
export const getStorageItem = (key, defaultValue = null) => {
  try {
    const item = window.localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    throw new Error(`Error reading localStorage key "${key}":`, error);
  }
};

/**
 * Set an item in localStorage
 * @param {string} key - The key to store the value under
 * @param {any} value - The value to store
 */
export const setStorageItem = (key, value) => {
  try {
    window.localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    throw new Error(`Error setting localStorage key "${key}":`, error);
  }
};

/**
 * Remove an item from localStorage
 * @param {string} key - The key to remove
 */
export const removeStorageItem = (key) => {
  try {
    window.localStorage.removeItem(key);
  } catch (error) {
    throw new Error(`Error removing localStorage key "${key}":`, error);
  }
};
