/**
 * Utility functions for handling multiple price options
 */

/**
 * Get size label for a given price index
 * @param {number} index - The price index
 * @param {Array} customLabels - Optional custom labels array
 * @returns {string} The size label
 */
export const getSizeLabel = (index, customLabels = null) => {
  if (customLabels && customLabels[index]) {
    return customLabels[index];
  }
  
  const defaultLabels = ['Small', 'Medium', 'Large', 'XL', 'XXL'];
  return defaultLabels[index] || `Option ${index + 1}`;
};

/**
 * Get the selected price for a food item
 * @param {Object} food - The food object
 * @param {number} selectedPriceIndex - The selected price index
 * @returns {number} The selected price
 */
export const getSelectedPrice = (food, selectedPriceIndex = 0) => {
  if (!food) return 0;
  
  // Handle new multiple price structure
  if (food.has_multiple_prices && food.prices && food.prices.length > 0) {
    return food.prices[selectedPriceIndex] || food.prices[0];
  }
  
  // Handle backward compatibility with single price
  return food.price || 0;
};

/**
 * Calculate display price with service fee
 * @param {number} basePrice - The base price
 * @param {number} serviceFeePercent - Service fee percentage (default 10%)
 * @returns {string} Formatted display price
 */
export const calculateDisplayPrice = (basePrice, serviceFeePercent = 10) => {
  const multiplier = 1 + (serviceFeePercent / 100);
  return (parseFloat(basePrice) * multiplier).toFixed(2);
};

/**
 * Format price range display
 * @param {Object} food - The food object
 * @returns {string} Formatted price range
 */
export const formatPriceRange = (food) => {
  if (!food) return 'GH₵0.00';
  
  if (food.has_multiple_prices && food.min_price && food.max_price) {
    if (food.min_price === food.max_price) {
      return `GH₵${food.min_price.toFixed(2)}`;
    }
    return `GH₵${food.min_price.toFixed(2)} - GH₵${food.max_price.toFixed(2)}`;
  }
  
  return `GH₵${(food.price || 0).toFixed(2)}`;
};

/**
 * Check if food has multiple price options
 * @param {Object} food - The food object
 * @returns {boolean} True if food has multiple prices
 */
export const hasMultiplePrices = (food) => {
  return food && food.has_multiple_prices && food.prices && food.prices.length > 1;
};

/**
 * Get all price options for a food item
 * @param {Object} food - The food object
 * @param {Array} customLabels - Optional custom labels
 * @returns {Array} Array of price options with labels
 */
export const getPriceOptions = (food, customLabels = null) => {
  if (!food) return [];
  
  if (hasMultiplePrices(food)) {
    return food.prices.map((price, index) => ({
      index,
      price,
      label: getSizeLabel(index, customLabels),
      displayPrice: `${getSizeLabel(index, customLabels)} - GH₵${price.toFixed(2)}`
    }));
  }
  
  return [{
    index: 0,
    price: food.price || 0,
    label: 'Standard',
    displayPrice: `GH₵${(food.price || 0).toFixed(2)}`
  }];
};
