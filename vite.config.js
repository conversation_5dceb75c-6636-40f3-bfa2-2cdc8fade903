import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { VitePWA } from 'vite-plugin-pwa';
import legacy from '@vitejs/plugin-legacy';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    // Add legacy browser support for iOS Safari
    legacy({
      targets: ['defaults', 'not IE 11', 'iOS >= 10'],
      additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
      renderLegacyChunks: true,
      polyfills: [
        'es.symbol',
        'es.array.filter',
        'es.promise',
        'es.promise.finally',
        'es/map',
        'es/set',
        'es.array.for-each',
        'es.object.define-properties',
        'es.object.define-property',
        'es.object.get-own-property-descriptor',
        'es.object.get-own-property-descriptors',
        'es.object.keys',
        'es.object.to-string',
        'web.dom-collections.for-each',
        'esnext.global-this',
        'esnext.string.match-all'
      ]
    }),
    VitePWA({
      // CHANGED: Use injectManifest strategy for custom service worker
      strategies: 'injectManifest',
      srcDir: 'src',
      filename: 'sw.js',
      registerType: 'prompt',
      injectManifest: {
        swSrc: 'src/sw.js',
        swDest: 'dist/sw.js', // Output location
        globDirectory: 'dist',
        globPatterns: [
          '**/*.{js,css,html,ico,png,svg,woff2}'
        ],
        // Don't include these files in precaching.cd 
        dontCacheBustURLsMatching: /\.\w{8}\./,
        maximumFileSizeToCacheInBytes: 5000000 // 5MB
      },
      includeAssets: ['favicon.ico', 'apple-touch-icon.png', 'masked-icon.svg', 'offline.html'],
      manifest: {
        name: 'EaseFood Delivery',
        short_name: 'EaseFood',
        description: 'Food delivery service in Dunkwa Offin',
        theme_color: '#ff000c',
        background_color: '#f8f9fa',
        display: 'standalone',
        start_url: '/',
        scope: '/',
        // Enable app badge support
        display_override: ['window-controls-overlay', 'standalone'],
        // Add iOS-specific manifest properties
        apple: {
          statusBarStyle: 'default'
        },
        icons: [
          {
            src: 'pwa-192x192.svg',
            sizes: '192x192',
            type: 'image/svg+xml',
          },
          {
            src: 'pwa-512x512.svg',
            sizes: '512x512',
            type: 'image/svg+xml',
          },
          {
            src: 'pwa-512x512.svg',
            sizes: '512x512',
            type: 'image/svg+xml',
            purpose: 'any maskable',
          },
          // iOS-specific icon
          {
            src: 'apple-touch-icon.png',
            sizes: '180x180',
            type: 'image/png',
          },
          {
            src: 'favicon.ico',
            sizes: '64x64 32x32 24x24 16x16',
            type: 'image/x-icon',
          },
        ],
      },
      // REMOVED: workbox config (now handled in custom service worker)
      devOptions: {
        enabled: true,
        type: 'module',
      }
    }),
  ],
  build: {
    target: 'es2015', // Ensure compatibility with older iOS Safari
    // Reduce chunk size to help with iOS memory constraints
    rollupOptions: {
      output: {
        manualChunks(id) {
          // Separate each dashboard type into its own chunk for better debugging
          if (id.includes('vendors-dashboard') || id.includes('VendorsDashboard')) {
            return 'vendors-dashboard';
          }
          if (id.includes('customer-orders') || id.includes('CustomerOrders')) {
            return 'customer-orders';
          }
          if (id.includes('admin-dashboard') || id.includes('AdminDashboard')) {
            return 'admin-dashboard';
          }
          if (id.includes('riders-dashboard') || id.includes('RidersDashboard')) {
            return 'riders-dashboard';
          }
          if (id.includes('node_modules')) {
            return 'vendor';
          }
        }
      }
    }
  },
  // Add server configuration for better iOS testing
  server: {
    host: true, // Allow external connections for testing on iOS
    port: 3000,
  },
  // CSS processing options for better Safari compatibility
  css: {
    postcss: {
      plugins: [
        // Add autoprefixer for webkit prefixes if you're using PostCSS
      ]
    }
  }
});